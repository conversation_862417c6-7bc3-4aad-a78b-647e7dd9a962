import { Router } from "express";
import {
  addTopCourse,
  getTopCourse,
  getTopCoaches,
  addTopCoach,
  getFaqs,
  getFaqById,
  createFaq,
  updateFaq,
  deleteFaq,
  getPolicy,
  createPolicy,
  updatePolicy,
  getBlocks,
  createBlock,
  updateBlock,
  getBlocksDataAdmin,
  updatePosition,
  getTestimonials,
  createTestimonials,
  updateTestimonials,
  getRegistrationInstructor,
  createRegistrationInstructor,
  updateRegistrationInstructor,
  deleteTestimonials,
  deleteRegistrationInstructor,
  uploadFile,
  updateTestimonialPosition,
  updateCoachPosition,
  updateCoursePosition,
  getTopCategories,
  addTopCategories,
  updateCategoryPosition,
  getAboutUs,
  createAboutUs,
  updateAboutUs,
  getRegistration,
  createRegistration,
  updateRegistration,
  createTermsAndCondition,
  updateTermsAndCondition,
  getTermsAndCondition,
  createPlayerTermsAndCondition,
  updatePlayerTermsAndCondition,
  getPlayerTermsAndCondition,
  getTopAcademies,
  addTopAcademy,
  updateAcademyPosition
} from "./cmsController.js";

import authenticateUserAccess from "../Helpers/authenticationUser.js";

const router = Router();
// File Upload
router.post("/upload", authenticateUserAccess("cms", "write"), uploadFile);

// Top Course
router.get("/course", getTopCourse);
router.post(
  "/course/add",
  authenticateUserAccess("cms", "write"),
  addTopCourse
);
router.patch(
  "/course/position",
  authenticateUserAccess("cms", "write"),
  updateCoursePosition
);

// Top Coach
router.get("/coach", getTopCoaches);
router.post("/coach/add", authenticateUserAccess("cms", "write"), addTopCoach);
router.patch(
  "/coach/position",
  authenticateUserAccess("cms", "write"),
  updateCoachPosition
);

// Top Category
router.get("/category", getTopCategories);
router.post(
  "/category/add",
  authenticateUserAccess("cms", "write"),
  addTopCategories
);
router.patch(
  "/category/position",
  authenticateUserAccess("cms", "write"),
  updateCategoryPosition
);

// Top Academy
router.get("/academy", getTopAcademies);
router.post(
  "/academy/add",
  authenticateUserAccess("cms", "write"),
  addTopAcademy
);
router.patch(
  "/academy/position",
  authenticateUserAccess("cms", "write"),
  updateAcademyPosition
);

// Faq
router.get("/faq", getFaqs);
router.get("/faq/:id", getFaqById);
router.post("/create/faq", authenticateUserAccess("cms", "write"), createFaq);
router.patch(
  "/update/faq/:id",
  authenticateUserAccess("cms", "write"),
  updateFaq
);
router.delete(
  "/delete/faq/:id",
  authenticateUserAccess("cms", "delete"),
  deleteFaq
);

// Policy
router.get("/policy", getPolicy);
router.post(
  "/create/policy",
  authenticateUserAccess("cms", "write"),
  createPolicy
);
router.patch(
  "/update/policy/:id",
  authenticateUserAccess("cms", "write"),
  updatePolicy
);

// About us
router.get("/cms-about-us", getAboutUs);
router.post(
  "/create/cms-about-us",
  authenticateUserAccess("cms", "write"),
  createAboutUs
);
router.patch(
  "/update/cms-about-us/:id",
  authenticateUserAccess("cms", "write"),
  updateAboutUs
);

// Registration Details
router.get("/cms-registration-details", getRegistration);
router.post(
  "/create/cms-registration-details",
  authenticateUserAccess("cms", "write"),
  createRegistration
);
router.patch(
  "/update/cms-registration-details/:id",
  authenticateUserAccess("cms", "write"),
  updateRegistration
);

// Terms And Condition Details
router.get("/cms-termsAndCondition-details", getTermsAndCondition);
router.post(
  "/create/cms-termsAndCondition-details",
  authenticateUserAccess("cms", "write"),
  createTermsAndCondition
);
router.patch(
  "/update/cms-termsAndCondition-details/:id",
  authenticateUserAccess("cms", "write"),
  updateTermsAndCondition
);


//player Terms and condition
router.get("/cms-player-termsAndCondition-details", getPlayerTermsAndCondition);
router.post(
  "/create/cms-player-termsAndCondition-details",
  authenticateUserAccess("cms", "write"),
  createPlayerTermsAndCondition
);
router.patch(
  "/update/cms-player-termsAndCondition-details/:id",
  authenticateUserAccess("cms", "write"),
  updatePlayerTermsAndCondition
);


// Testimonials
router.get("/testimonials", getTestimonials);
router.post(
  "/create/testimonial",
  authenticateUserAccess("cms", "write"),
  createTestimonials
);
router.patch(
  "/update/testimonial/position",
  authenticateUserAccess("cms", "write"),
  updateTestimonialPosition
);
router.patch(
  "/update/testimonial/:id",
  authenticateUserAccess("cms", "write"),
  updateTestimonials
);
router.delete(
  "/delete/testimonial/:id",
  authenticateUserAccess("cms", "delete"),
  deleteTestimonials
);

// Registration Instructor
router.get("/registration-instructor", getRegistrationInstructor);
router.post(
  "/create/registration-instructor",
  authenticateUserAccess("cms", "write"),
  createRegistrationInstructor
);
router.patch(
  "/update/registration-instructor/:id",
  authenticateUserAccess("cms", "write"),
  updateRegistrationInstructor
);
router.delete(
  "/delete/registration-instructor/:id",
  authenticateUserAccess("cms", "delete"),
  deleteRegistrationInstructor
);

// Blocks
router.get("/blocks", getBlocks);
router.get(
  "/admin/blocks",
  authenticateUserAccess("cms", "read"),
  getBlocksDataAdmin
);
router.post(
  "/create/block",
  authenticateUserAccess("cms", "write"),
  createBlock
);
router.patch(
  "/update/block/position",
  authenticateUserAccess("cms", "write"),
  updatePosition
);
router.patch(
  "/update/block/:id",
  authenticateUserAccess("cms", "write"),
  updateBlock
);
export default router;
