import { Course } from "./courseModal.js";
import validator from "validator";
import moment from "moment";
import { multerUpload } from "../utils/awsHelper.js";
import { SECRETS } from "../utils/config.js";
import AWS from "aws-sdk";
import { daysDifference, formatDateToYYYYMMDD } from "../utils/datehelper.js";
import {
  createBookingEvents,
  deleteEventByParams,
  updateBookingEvents,
} from "../calendar/calendarController.js";
import { Booking } from "../bookings/bookingModal.js";
import { Coach } from "../coaches/coachModal.js";
import { AcademyTopCourse } from "../academyModule/cms/model/academyTopCourseModal.js";

const AWS_ACCESS_KEY_ID = SECRETS.AWS_ACCESS_KEY_ID;
const AWS_SECRET_ACCESS_KEY = SECRETS.AWS_SECRET_ACCESS_KEY;
const AWS_BUCKET_NAME = SECRETS.AWS_BUCKET_NAME;
const AWS_S3_REGION = SECRETS.AWS_S3_REGION;

AWS.config.update({
  accessKeyId: AWS_ACCESS_KEY_ID,
  secretAccessKey: AWS_SECRET_ACCESS_KEY,
  region: AWS_S3_REGION,
});
const s3 = new AWS.S3();

export const getCourses = async (req, res) => {
  try {
    let {
      page,
      coach_id,
      courseName,
      classType,
      status,
      getAllCourses = true,
    } = req.query;
    page = page || 1;
    let limitVal = 25;
    let skipValue = (page - 1) * limitVal;
    let query = {};

    if (req.userType === "academy" && req.user?.academyId?._id) {
      query.academy_id = req.user.academyId._id;
    } else if (req.userType === "Admin" && !getAllCourses) {
      query.$or = [
        { academy_id: { $exists: false } },
        { academy_id: null },
        { academy_id: "" },
      ];
    }

    if (courseName) {
      query.courseName = { $regex: courseName, $options: "i" };
    }

    if (classType) {
      query.classType = classType;
    }
    if (status) {
      query.status = status;
    }
    if (coach_id) {
      query.coach_id = coach_id;
    }
    const totalResults = await Course.countDocuments(query);
    const data = await Course.find(query)
      .skip(skipValue)
      .limit(limitVal)
      .populate("academy_id", "name")
      .sort({ createdAt: -1 });
    return res.status(200).json({
      data,
      totalResults,
      currentPage: page,
      totalPages: Math.ceil(totalResults / limitVal),
    });
  } catch (e) {
    res.status(500).json({ error: "Internal server error" });
  }
};

export const getCourseById = async (req, res) => {
  try {
    const id = req.params.id;
    if (!id) {
      return res.status(400).json({ error: "id is required" });
    }
    const data = await Course.findById(id)
      .populate(["coach_id"])
      .populate("academy_id", "name platformFee");
    if (!data) {
      return res.status(400).json({ error: "Course Not found" });
    }
    res.status(200).json(data);
  } catch (e) {
    res.status(500).json({ error: "Internal server error" });
  }
};

export const getCourseByCoachId = async (req, res) => {
  try {
    const id = req.params.id;
    if (!id) {
      return res.status(400).json({ error: "id is required" });
    }
    let { page } = req.query;
    page = page || 1;
    let limitVal = 25;
    let skipValue = (page - 1) * limitVal;
    const totalResults = await Course.countDocuments({ coach_id: id });
    const data = await Course.find({ coach_id: id })
      .populate(["coach_id"])
      .populate("academy_id", "name platformFee")
      .skip(skipValue)
      .limit(limitVal)
      .sort({ createdAt: -1 });
    res.status(200).json({ data, totalResults });
  } catch (e) {
    res.status(500).json({ error: "Internal server error" });
  }
};

export const createCourse = async (req, res) => {
  try {
    const {
      coach_id,
      coachName,
      courseName,
      description,
      images,
      facility,
      category,
      sessionType,
      classType,
      camp,
      campName,
      maxGroupSize,
      proficiency,
      fees,
      dates,
      amenitiesProvided,
      cancellationPolicy,
      whatYouHaveToBring,
    } = req.body;

    if (req.userType === "academy" && req.user?.academyId?._id) {
      const coach = await Coach.findOne({
        _id: coach_id,
        academyId: req.user.academyId._id,
      });
      if (!coach) {
        return res.status(400).json({
          error: "Coach not found or not authorized to create course",
        });
      }
      req.body.academy_id = req.user.academyId._id;
    } else if (req.userType === "Admin") {
      const coach = await Coach.findById(coach_id);
      if (!coach || coach.affiliationType === "academy") {
        return res.status(400).json({
          error: "Coach not found or not authorized to create course",
        });
      }
    }

    const coach = await Coach.findById(coach_id);
    if (!coach.refreshToken || coach.refreshToken == "") {
      return res
        .status(400)
        .json({ error: "Cannot create course if not linked with google" });
    }
    console.log(req.body.academy_id, "academy_id");
    const academyIdCoach = coach?.academyId.toString() || null;
    console.log(academyIdCoach, "academyIdCoach");
    if (academyIdCoach) {
      req.body.academy_id = academyIdCoach;
    }

    const dateHelper = await getDatesByDays(
      new Date(dates.startDate.split("T")[0]),
      new Date(dates.endDate.split("T")[0]),
      dates.days
    );
    const addObj = {
      ...req.body,
      dates: {
        startDate: req.body.dates.startDate,
        endDate: req.body.dates.endDate,
        startTime: req.body.dates.startTime,
        endTime: req.body.dates.endTime,
        days: req.body.dates.days,
        dates: dateHelper,
      },
    };
    if (!coachName || !coach_id) {
      return res.status(400).json({ error: "Coach is required" });
    }
    if (!courseName || !validator.isLength(courseName, { min: 3 })) {
      return res
        .status(400)
        .json({ error: "Course name must have at least 3 characters" });
    }
    if (!description || !validator.isLength(description, { min: 5 })) {
      return res
        .status(400)
        .json({ error: "description must have at least 5 characters" });
    }
    if (!facility.name) {
      return res.status(400).json({ error: "Facility is required" });
    }
    if (!category) {
      return res.status(400).json({ error: "Category is required" });
    }
    if (!images || images.length <= 0) {
      return res.status(400).json({ error: "image is required" });
    }
    if (camp && !validator.isLength(campName, { min: 3 })) {
      return res
        .status(400)
        .json({ error: "Camp name must have at least 3 characters" });
    }
    if (!sessionType) {
      return res.status(400).json({ error: "Session type is required" });
    }
    if (!classType) {
      return res.status(400).json({ error: "Class type is required" });
    }
    if (!maxGroupSize) {
      return res.status(400).json({ error: "Max group size is required" });
    }
    if (!proficiency || proficiency.length <= 0) {
      return res.status(400).json({ error: "Proficiency level is required" });
    }
    if (classType === "class") {
      if (!fees.fees30 && !fees.fees45 && !fees.fees60) {
        return res.status(400).json({ error: "fees is required" });
      }
    } else {
      if (!fees.feesCourse) {
        return res.status(400).json({ error: "Course fees is required" });
      }
    }
    // Dates Validation
    // Validate start date and end date
    const isValidStartDate = moment(dates.startDate).isSameOrAfter(
      moment(),
      "day"
    );
    const isValidEndDate = moment(dates.endDate).isSameOrAfter(moment(), "day");
    const isValidDateRange = moment(dates.endDate).isSameOrAfter(
      moment(dates.startDate),
      "day"
    );

    // Validate start time and end time
    const isValidStartTime = moment(dates.startTime, "HH:mm", true).isValid();
    const isValidEndTime = moment(dates.endTime, "HH:mm", true).isValid();

    // Validate days array
    const validDays = ["Mon", "Tue", "Wed", "Thu", "Fri", "Sat", "Sun"];
    const isValidDays = dates.days.every((dayObj) =>
      validDays.includes(dayObj)
    );

    // Check if any of the validations failed
    if (!isValidStartDate) {
      return res
        .status(400)
        .json({ error: "Start date cannot be in the past" });
    }
    if (
      !isValidEndDate ||
      !isValidDateRange ||
      !isValidStartTime ||
      !isValidEndTime ||
      !isValidDays
    ) {
      return res.status(400).json({ error: "Add valid date object" });
    }

    const validationResult = await validateCoachAcademyAvailability(
      coach_id,
      dates
    );
    if (!validationResult.isValid) {
      return res.status(400).json({ error: validationResult.error });
    }

    const data = await Course.create(addObj);

    if (req.user?.academyId?._id) {
      try {
        const topCourseEntry = await AcademyTopCourse.create({
          academy: req.user.academyId._id,
          course: data._id,
        });
      } catch (err) {
        console.error("Error message:", err.message);
      }
    }

    const endDate = `${formatDateToYYYYMMDD(dateHelper[0])}T${
      dates.endTime
    }:00`;
    const startDate = `${formatDateToYYYYMMDD(dateHelper[0])}T${
      dates.startTime
    }:00`;
    const event = await createBookingEvents({
      summary: courseName,
      startDateTime: startDate,
      endDateTime: endDate,
      days: dates.days,
      description: JSON.stringify({
        id: data._id,
        type: "course",
        classType: classType === "class" ? "class" : "course",
      }),
      daysCount: daysDifference(dates.startDate, dates.endDate, dates.days),
      coachId: coach_id,
      colorId: classType === "class" ? "4" : "2",
    });
    if (event.status === 200) {
      await Course.findByIdAndUpdate(data._id, { eventId: event.data.data.id });
      return res.status(200).json(data);
    } else {
      return res.status(event.status).json({ error: event.error });
    }
  } catch (e) {
    console.log(e, "course creation error");
    return res.status(500).json({ error: "Internal server error" });
  }
};

// Helper function to calculate dates

export function getDatesByDays(startDate, endDate, days) {
  return new Promise((resolve, reject) => {
    try {
      const dates = [];
      let currentDate = new Date(startDate);
      const end = new Date(endDate);
      end.setDate(end.getDate() + 1); // Increment endDate by 1 day to include the last day

      while (currentDate < end) {
        const currentDayOfWeek = getDayOfWeek(currentDate);

        if (days.includes(currentDayOfWeek)) {
          dates.push(new Date(currentDate));
        }

        currentDate.setDate(currentDate.getDate() + 1);
      }

      resolve(dates);
    } catch (error) {
      reject(error);
    }
  });
}

function getDayOfWeek(date) {
  const days = ["Sun", "Mon", "Tue", "Wed", "Thu", "Fri", "Sat"];
  return days[date.getDay()];
}

export const updateCourse = async (req, res) => {
  try {
    if (!req.auth) {
      return res.status(400).json({ error: "Access not allowed" });
    }
    const id = req.params.id;
    if (!id) {
      return res.status(400).json({ error: "id is required" });
    }
    const {
      coach_id,
      coachName,
      courseName,
      description,
      images,
      facility,
      category,
      sessionType,
      classType,
      camp,
      campName,
      maxGroupSize,
      proficiency,
      fees,
      dates,
      eventId,
      amenitiesProvided,
      cancellationPolicy,
      whatYouHaveToBring,
    } = req.body;

    if (req.userType === "academy" && req.user?.academyId?._id) {
      const coach = await Coach.findOne({
        _id: coach_id,
        academyId: req.user.academyId._id,
      });
      if (!coach) {
        return res.status(400).json({
          error: "Coach not found or not authorized to update course",
        });
      }
      req.body.academy_id = req.user.academyId._id;
    } else if (req.userType === "Admin") {
      const coach = await Coach.findById(coach_id);
      if (!coach || coach.affiliationType === "academy") {
        return res.status(400).json({
          error: "Coach not found or not authorized to update course",
        });
      }
    }

    const dateHelper = await getDatesByDays(
      new Date(dates.startDate.split("T")[0]),
      new Date(dates.endDate.split("T")[0]),
      dates.days
    );
    const addObj = {
      ...req.body,
      dates: {
        startDate: req.body.dates.startDate,
        endDate: req.body.dates.endDate,
        startTime: req.body.dates.startTime,
        endTime: req.body.dates.endTime,
        days: req.body.dates.days,
        dates: dateHelper,
      },
    };
    if (!coachName || !coach_id) {
      return res.status(400).json({ error: "Coach is required" });
    }
    if (!courseName || !validator.isLength(courseName, { min: 3 })) {
      return res
        .status(400)
        .json({ error: "Course name must have at least 3 characters" });
    }
    if (!description || !validator.isLength(description, { min: 5 })) {
      return res
        .status(400)
        .json({ error: "description must have at least 5 characters" });
    }
    if (!facility.name) {
      return res.status(400).json({ error: "Facility is required" });
    }
    if (!category) {
      return res.status(400).json({ error: "Category is required" });
    }
    if (!images || images.length <= 0) {
      return res.status(400).json({ error: "image is required" });
    }
    if (camp && !validator.isLength(campName, { min: 3 })) {
      return res
        .status(400)
        .json({ error: "Camp name must have at least 3 characters" });
    }
    if (!sessionType) {
      return res.status(400).json({ error: "Session type is required" });
    }
    if (!classType) {
      return res.status(400).json({ error: "Class type is required" });
    }
    if (!maxGroupSize) {
      return res.status(400).json({ error: "Max group size is required" });
    }
    if (!proficiency || proficiency.length <= 0) {
      return res.status(400).json({ error: "Proficiency level is required" });
    }
    if (classType === "class") {
      if (!fees.fees30 && !fees.fees45 && !fees.fees60) {
        return res.status(400).json({ error: "fees is required" });
      }
    } else {
      if (!fees.feesCourse) {
        return res.status(400).json({ error: "Course fees is required" });
      }
    }
    // Dates Validation
    // Validate start date and end date
    const isValidStartDate = moment(dates.startDate).isSameOrAfter(
      moment(),
      "day"
    );
    const isValidEndDate = moment(dates.endDate).isSameOrAfter(moment(), "day");
    const isValidDateRange = moment(dates.endDate).isSameOrAfter(
      moment(dates.startDate),
      "day"
    );

    // Validate start time and end time
    const isValidStartTime = moment(dates.startTime, "HH:mm", true).isValid();
    const isValidEndTime = moment(dates.endTime, "HH:mm", true).isValid();

    // Validate days array
    const validDays = ["Mon", "Tue", "Wed", "Thu", "Fri", "Sat", "Sun"];
    const isValidDays = dates.days.every((dayObj) =>
      validDays.includes(dayObj)
    );

    // Check if any of the validations failed
    if (!isValidStartDate) {
      return res
        .status(400)
        .json({ error: "Start date cannot be in the past" });
    }
    if (
      !isValidEndDate ||
      !isValidDateRange ||
      !isValidStartTime ||
      !isValidEndTime ||
      !isValidDays
    ) {
      return res.status(400).json({ error: "Add valid date object" });
    }

    const validationResult = await validateCoachAcademyAvailability(
      coach_id,
      dates
    );
    if (!validationResult.isValid) {
      return res.status(400).json({ error: validationResult.error });
    }

    const dateTimeString = `${formatDateToYYYYMMDD(dates.startDate)}T${
      dates.startTime
    }:00`;
    const endDate = `${formatDateToYYYYMMDD(dates.startDate)}T${
      dates.endTime
    }:00`;

    const endTimeString = `${formatDateToYYYYMMDD(dates.endDate)}T${
      dates.endTime
    }:00`;
    const event = await updateBookingEvents({
      summary: courseName,
      startDateTime: dateTimeString,
      endDateTime: endDate,
      days: dates.days,
      description: JSON.stringify({
        id: id,
        type: "course",
        classType: classType === "class" ? "class" : "course",
      }),
      daysCount: daysDifference(dateTimeString, endTimeString, dates.days),
      coachId: coach_id,
      colorId: classType === "class" ? "4" : "2",
      eventId,
    });
    const data = await Course.findByIdAndUpdate(id, addObj, { new: true });
    res.status(200).json(data);
  } catch (e) {
    res.status(500).json({ error: "Internal server error" });
  }
};

export const deleteCourse = async (req, res) => {
  try {
    if (!req.auth) {
      return res.status(400).json({ error: "Access not allowed" });
    }
    const id = req.params.id;
    if (!id) {
      return res.status(400).json({ error: "id is required" });
    }
    if (req.userType === "academy" && req.user?.academyId?._id) {
      const course = await Course.findOne({
        _id: id,
        academy_id: req.user.academyId._id,
      });
      if (!course) {
        return res
          .status(400)
          .json({ error: "Course not found or not authorized to delete" });
      }
    } else if (req.userType === "Admin") {
      const course = await Course.findById(id);
      if (!course) {
        return res.status(400).json({ error: "Course not found" });
      }
      if (course.academy_id) {
        return res.status(400).json({
          error:
            "Course is affiliated with an academy so can not delete it directly",
        });
      }
    }
    const bookings = await Booking.find({ coachId: id });
    // we will add one condition of if someone booked then we can not delete
    if (bookings) {
      return res
        .status(400)
        .json({ error: "Can not delete course With booking" });
    }
    const data = await Course.findByIdAndDelete(id);

    if (data) {
      const deletedEvent = await deleteEventByParams(
        data.coach_id,
        data.eventId
      );
      if (deletedEvent.status === 200) {
        return res.status(200).json({ message: deletedEvent.message });
      } else {
        return res
          .status(deletedEvent.status)
          .json({ error: deletedEvent.error });
      }
    } else {
      return res.status(400).json({ error: "id not found" });
    }
  } catch (e) {
    res.status(500).json({ error: "Internal server error" });
  }
};

export const uploadFile = async (req, res) => {
  try {
    const uploadSingle = multerUpload(AWS_BUCKET_NAME, "images2/course").single(
      "image"
    );

    uploadSingle(req, res, async (err) => {
      if (err) {
        return res.status(400).json({ error: err.message });
      }
      const url = req.body.url;
      if (url) {
        const deleteParams = {
          Bucket: AWS_BUCKET_NAME,
          Key: `images2/course/${url.split("/").pop()}`,
        };
        const deleteResult = await s3.deleteObject(deleteParams).promise();
      }
      // Assuming your uploaded file is public
      // const publicUrl = `${AWS_S3_BASE_URL}/${AWS_BUCKET_NAME}/${req?.file?.key}`;

      return res.status(200).json({ url: req?.file?.location });
    });
  } catch (error) {
    res.status(500).json({
      error: error.message || "Internal server error",
    });
  }
};

export const getAvailableSlots = async (req, res) => {
  try {
    if (!req.auth) {
      return res.status(400).json({ error: "Access not allowed" });
    }
    const { id } = req.params;
    if (!id) {
      return res.status(400).json({ error: "Coach ID is required" });
    }

    const { courseId, startDate, endDate, startTime, endTime, days } =
      req.body.dates;
    const dates = await getDatesByDays(
      new Date(startDate.split("T")[0]),
      new Date(endDate.split("T")[0]),
      days
    );

    const queryConditions = [
      { coach_id: id }, // Filter by coach ID
      {
        "dates.dates": { $elemMatch: { $in: dates } },
      },
      { "dates.days": { $in: days } }, // Check for matching days
      {
        $or: [
          {
            $and: [
              { "dates.startTime": { $lt: endTime } }, // Check if start time is before or equal to end time
              { "dates.endTime": { $gt: startTime } }, // Check if end time is after or equal to start time
            ],
          },
          {
            $and: [
              { "dates.startTime": { $gt: startTime, $lt: endTime } }, // Check if start time is within the range
              { "dates.endTime": { $gt: endTime } }, // Check if end time is after the range
            ],
          },
          {
            $and: [
              { "dates.startTime": { $lt: startTime } }, // Check if start time is before the range
              { "dates.endTime": { $gt: startTime, $lt: endTime } }, // Check if end time is within the range
            ],
          },
        ],
      },
    ];

    // If courseId is provided, add it to the query conditions
    if (courseId) {
      queryConditions.push({ _id: { $ne: courseId } }); // Exclude the provided courseId
    }

    // Find courses for the specific coach that have overlapping schedules
    const conflictingSlots = await Course.find({ $and: queryConditions });

    if (conflictingSlots.length > 0) {
      // Prepare response with conflicting slots
      const conflictingDates = conflictingSlots.map((course) => ({
        courseId: course._id,
        startDate: course.dates.startDate,
        endDate: course.dates.endDate,
        conflictingDays: course.dates.days.filter((day) => days.includes(day)),
        conflictingStartTime: course.dates.startTime,
        conflictingEndTime: course.dates.endTime,
      }));
      return res
        .status(200)
        .json({ message: "Conflicting slots", conflictingDates });
    } else {
      return res.status(200).json({ message: "Slots available" });
    }
  } catch (e) {
    return res.status(500).json({ error: "Internal server Error" });
  }
};

export const courseCron = async () => {
  try {
    const currentDate = moment().tz("Asia/Kolkata").format("YYYY-MM-DD");

    // Update classes with classType === "class" and endDate less than today's date
    await Course.updateMany(
      {
        classType: "class",
        status: "active",
        "dates.endDate": { $lt: new Date(currentDate) },
      },
      { $set: { status: "inactive" } }
    );

    // Update courses with startDate less than today's date and status active
    await Course.updateMany(
      {
        classType: "course",
        status: "active",
        "dates.startDate": { $lt: new Date(currentDate) },
      },
      { $set: { status: "inactive" } }
    );

    // Clean up academy top courses for inactive courses
    await cleanupAcademyTopCourses();

    console.log(`Class cron ${new Date(currentDate)} `);
  } catch (error) {
    console.error("Error updating course status:", error);
  }
};

// New function to clean up academy top courses
const cleanupAcademyTopCourses = async () => {
  try {
    // Import the AcademyTopCourse model
    const { AcademyTopCourse } = await import(
      "../academyModule/cms/model/academyTopCourseModal.js"
    );

    // Find all academy top courses that reference inactive courses
    const inactiveTopCourses = await AcademyTopCourse.find({
      course: {
        $in: await Course.find({ status: "inactive" }).distinct("_id"),
      },
    }).populate("course", "status");

    // Group by academy for batch processing
    const academyGroups = {};
    inactiveTopCourses.forEach((topCourse) => {
      if (!academyGroups[topCourse.academy]) {
        academyGroups[topCourse.academy] = [];
      }
      academyGroups[topCourse.academy].push(topCourse);
    });

    // Process each academy separately
    for (const [academyId, topCourses] of Object.entries(academyGroups)) {
      // Delete inactive top courses for this academy
      await AcademyTopCourse.deleteMany({
        academy: academyId,
        course: {
          $in: topCourses.map((tc) => tc.course._id),
        },
      });

      // Reorder remaining top courses for this academy
      const remainingTopCourses = await AcademyTopCourse.find({
        academy: academyId,
      }).sort({ position: 1 });

      // Update positions sequentially
      for (let i = 0; i < remainingTopCourses.length; i++) {
        await AcademyTopCourse.findByIdAndUpdate(remainingTopCourses[i]._id, {
          position: i + 1,
        });
      }
    }

    console.log(
      `Cleaned up ${inactiveTopCourses.length} inactive top courses across ${
        Object.keys(academyGroups).length
      } academies`
    );
  } catch (error) {
    console.error("Error cleaning up academy top courses:", error);
  }
};

export const giveRating = async (req, res) => {
  try {
    if (!req.auth) {
      return res.status(400).json({ error: "Access not allowed" });
    }
    const { id } = req.params;
    let { stars } = req.body;

    if (!id) {
      return res.status(400).json({ error: "Course Id is required" });
    }

    if (stars < 1 || stars > 5) {
      return res.status(400).json({ error: "Stars should be between 1 and 5" });
    }

    // Ensure stars are rounded to one decimal place
    stars = parseFloat(stars.toFixed(1));

    const book = await Booking.findById(id);
    if (book.status !== "Inactive" || book.rating !== false) {
      return res.status(400).json({ error: "Can't rate this course now" });
    }

    const booking = await Booking.findByIdAndUpdate(
      id,
      { rating: true, stars },
      { new: true }
    );

    const course = await Course.findByIdAndUpdate(
      booking.courseId,
      {
        $inc: { "ratings.likes": stars, "ratings.noOfRatings": 1 },
        // Calculate the new value of ratings.stars before updating
      },
      { new: true }
    );

    // Calculate the new value of ratings.stars
    const newStarsValue = parseFloat(
      (course.ratings.likes / course.ratings.noOfRatings).toFixed(1)
    );
    // Update the course document with the new stars value
    course.ratings.stars = newStarsValue;
    await course.save();

    const coachCourses = await Course.find({
      coach_id: course.coach_id,
      "ratings.likes": { $gt: 0 },
    });
    let totalStars = 0;
    for (const coachCourse of coachCourses) {
      totalStars += coachCourse.ratings.stars || 0; // Treat undefined as 0
    }

    const coachRating = coachCourses.length
      ? parseFloat((totalStars / coachCourses.length).toFixed(1)) // Rounded to one decimal place
      : 0;

    const coach = await Coach.findByIdAndUpdate(
      course.coach_id,
      { ratings: coachRating },
      { new: true }
    );

    return res.status(200).json({ message: "Rating submitted successfully" });
  } catch (e) {
    return res.status(500).json({ error: "Internal server error" + e.message });
  }
};

const validateCoachAcademyAvailability = async (coachId, courseDates) => {
  try {
    const coach = await Coach.findById(coachId);
    if (!coach) {
      return { isValid: false, error: "Coach not found" };
    }

    // Only validate if coach is affiliated with an academy
    if (coach.affiliationType !== "academy" || !coach.academyAvailability) {
      return { isValid: true }; // No validation needed for individual coaches
    }

    const academyAvail = coach.academyAvailability;

    // Check if academy availability has required fields
    if (
      !academyAvail.startDate ||
      !academyAvail.endDate ||
      !academyAvail.startTime ||
      !academyAvail.endTime ||
      !academyAvail.days ||
      academyAvail.days.length === 0
    ) {
      return {
        isValid: false,
        error: "Coach academy availability is not properly configured",
      };
    }

    const courseStartDate = moment(courseDates.startDate);
    const courseEndDate = moment(courseDates.endDate);
    const academyStartDate = moment(academyAvail.startDate);
    const academyEndDate = moment(academyAvail.endDate);

    // Check if course dates are within academy date range
    if (
      courseStartDate.isBefore(academyStartDate, "day") ||
      courseEndDate.isAfter(academyEndDate, "day")
    ) {
      return {
        isValid: false,
        error: `Course dates must be within academy availability period: ${academyStartDate.format(
          "YYYY-MM-DD"
        )} to ${academyEndDate.format("YYYY-MM-DD")}`,
      };
    }

    // Check if course days are within academy available days
    const courseDays = courseDates.days;
    const academyDays = academyAvail.days;

    const hasValidDays = courseDays.every((day) => academyDays.includes(day));
    if (!hasValidDays) {
      return {
        isValid: false,
        error: `Course days (${courseDays.join(
          ", "
        )}) must be within academy available days (${academyDays.join(", ")})`,
      };
    }

    // Check if course time is within academy time range
    const courseStartTime = moment(courseDates.startTime, "HH:mm");
    const courseEndTime = moment(courseDates.endTime, "HH:mm");
    const academyStartTime = moment(academyAvail.startTime, "HH:mm");
    const academyEndTime = moment(academyAvail.endTime, "HH:mm");

    if (
      courseStartTime.isBefore(academyStartTime) ||
      courseEndTime.isAfter(academyEndTime)
    ) {
      return {
        isValid: false,
        error: `Course time (${courseDates.startTime}-${courseDates.endTime}) must be within academy time range (${academyAvail.startTime}-${academyAvail.endTime})`,
      };
    }

    return { isValid: true };
  } catch (error) {
    return { isValid: false, error: "Error validating academy availability" };
  }
};
