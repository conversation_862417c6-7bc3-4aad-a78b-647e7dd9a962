import mongoose from "mongoose";
const { Schema, model } = mongoose;

const academyTopCoachSchema = new Schema(
  {
    academy: { type: Schema.Types.ObjectId, ref: "academy" },
    coach: { type: Schema.Types.ObjectId, ref: "coach" },
    position: { type: Number },
  },
  { timestamps: true }
);

// Pre-save middleware to automatically assign position
academyTopCoachSchema.pre("save", async function (next) {
  try {
    if (!this.isNew || typeof this.position !== "undefined") {
      return next(); // If not new or position already defined, skip
    }
    const maxPosition = await this.constructor.findOne(
      { academy: this.academy }, // Scope to this academy
      {},
      { sort: { position: -1 } }
    ); // Find the document with maximum position
    this.position = maxPosition ? maxPosition.position + 1 : 1; // Assign the next position
    next();
  } catch (error) {
    next(error);
  }
});

// Create compound index to prevent duplicate academy-coach combinations
academyTopCoachSchema.index({ academy: 1, coach: 1 }, { unique: true });

export const AcademyTopCoach = model(
  "academyTopCoachCms",
  academyTopCoachSchema
);
