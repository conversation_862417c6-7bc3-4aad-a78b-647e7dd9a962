import express from "express";
import authenticateUserAccess from "../../Helpers/authenticationUser.js";

import {
  createUser,
  deleteUserById,
  getUserById,
  loginUser,
  readAllUsers,
  updateUserById,
} from "./userController.js";
const UserRoute = express.Router();

UserRoute.get("/all", authenticateUserAccess("user", "read"), readAllUsers);
UserRoute.post("/login", loginUser);
UserRoute.post("/", createUser);
UserRoute.get("/:id", authenticateUserAccess("user", "read"), getUserById);
UserRoute.put("/:id", authenticateUserAccess("user", "write"), updateUserById);
UserRoute.delete(
  "/:id",
  authenticateUserAccess("user", "delete"),
  deleteUserById
);

export default UserRoute;
