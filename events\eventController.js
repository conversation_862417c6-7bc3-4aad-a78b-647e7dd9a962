import {
  createBookingEvents,
  deleteEventByParams,
} from "../calendar/calendarController.js";
import { formatDateToYYYYMMDD } from "../utils/datehelper.js";
import { Events } from "./eventModal.js";

export const getEvents = async (req, res) => {
  try {
    if (!req.auth) {
      return res.status(400).json({ error: "Access not allowed" });
    }
    let { page, name, coachId } = req.query;
    page = page || 1;
    let limitVal = 25;
    let skipValue = (page - 1) * limitVal;
    let query = {};
    if (name) {
      query.name = { $regex: name, $options: "i" };
    }
    if (coachId) {
      query.coachId = coachId;
    }
    const totalResults = await Events.countDocuments(query);
    const data = await Events.find(query)
      .skip(skipValue)
      .limit(limitVal)
      .sort({ createdAt: -1 });
    return res.status(200).json({ data, totalResults });
  } catch (e) {
    return res.status(500).json({ error: "Internal server error" });
  }
};

export const getEventById = async (req, res) => {
  try {
    if (!req.auth) {
      return res.status(400).json({ error: "Access not allowed" });
    }
    const id = req.params.id;
    if (!id) {
      return res.status(400).json({ error: "id is required" });
    }
    const data = await Events.findById(id);
    if (!data) {
      return res.status(400).json({ error: "Event Not found" });
    }
    return res.status(200).json(data);
  } catch (e) {
    return res.status(500).json({ error: "Internal server error" });
  }
};

export const createEvent = async (req, res) => {
  try {
    if (!req.auth) {
      return res.status(400).json({ error: "Access not allowed" });
    }
    const {
      summary,
      startDateTime,
      endDateTime,
      days,
      daysCount,
      colorId,
      endDate,
    } = req.body;
    const { coachId } = req.query;
    if (!coachId) {
      return res.status(400).json({ error: "Coach Id is required" });
    }

    if (!summary || !startDateTime || !endDateTime || !days || !colorId) {
      return res.status(400).json({ error: "Please fill all details" });
    }
    const dateHelper = await getDatesByDays(
      new Date(startDateTime.split("T")[0]),
      new Date(endDate.split("T")[0]),
      days
    );

    const data = await Events.create({
      name: summary,
      coachId,
      startDate: dateHelper[0],
      endDate: dateHelper[dateHelper.length - 1],
      days,
      dates: dateHelper,
      startTime: startDateTime.toString().split("T")[1].substring(0, 5),
      endTime: endDateTime.toString().split("T")[1].substring(0, 5),
    });
    if (data) {
      const endDate = `${formatDateToYYYYMMDD(dateHelper[0])}T${endDateTime
        .toString()
        .split("T")[1]
        .substring(0, 5)}:00`;
      const startDate = `${formatDateToYYYYMMDD(dateHelper[0])}T${startDateTime
        .toString()
        .split("T")[1]
        .substring(0, 5)}:00`;
      const event = await createBookingEvents({
        summary,
        description: JSON.stringify({
          id: data._id,
          type: "event",
        }),
        startDateTime: startDate,
        endDateTime: endDate,
        days,
        daysCount,
        coachId,
        colorId,
      });
      if (event.status === 200) {
        await Events.findByIdAndUpdate(
          data._id,
          {
            eventId: event.data.data.id,
          },
          { new: true }
        );
        return res.status(200).json({ message: "Event Created Successfully" });
      } else {
        return res.status(event.status).json({ error: event.error });
      }
    }
  } catch (e) {
    return res.status(500).json({ error: "Internal server error" });
  }
};
function getDatesByDays(startDate, endDate, days) {
  return new Promise((resolve, reject) => {
    try {
      const dates = [];
      let currentDate = new Date(startDate);
      const end = new Date(endDate);
      end.setDate(end.getDate() + 1); // Increment endDate by 1 day to include the last day

      while (currentDate < end) {
        const currentDayOfWeek = getDayOfWeek(currentDate);

        if (days.includes(currentDayOfWeek)) {
          dates.push(new Date(currentDate));
        }

        currentDate.setDate(currentDate.getDate() + 1);
      }

      resolve(dates);
    } catch (error) {
      reject(error);
    }
  });
}

function getDayOfWeek(date) {
  const days = ["Sun", "Mon", "Tue", "Wed", "Thu", "Fri", "Sat"];
  return days[date.getDay()];
}

export const deleteEvent = async (req, res) => {
  try {
    if (!req.auth) {
      return res.status(400).json({ error: "Access not allowed" });
    }
    const coachId = req.params.id;
    if (!coachId) {
      return res.status(400).json({ error: "Coach Id is required" });
    }
    const { eventId } = req.body;
    if (!eventId) {
      return res.status(400).json({ error: "Event Id is required" });
    }
    const event = await Events.findOneAndDelete({ eventId });
    if (event) {
      const deletedEvent = await deleteEventByParams(coachId, eventId);
      if (deletedEvent.status === 200) {
        return res.status(200).json({ message: deletedEvent.message });
      } else {
        return res
          .status(deletedEvent.status)
          .json({ error: deletedEvent.error });
      }
    } else {
      return res.status(400).json({ error: "Event not found" });
    }
  } catch (e) {
    return res.status(500).json({ error: "Internal server error" });
  }
};
