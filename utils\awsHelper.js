import <PERSON><PERSON> from "aws-sdk";
import multer from "multer";
import multerS3 from "multer-s3";
import { SECRETS } from "./config.js";
const AWS_ACCESS_KEY_ID = SECRETS.AWS_ACCESS_KEY_ID;
const AWS_SECRET_ACCESS_KEY = SECRETS.AWS_SECRET_ACCESS_KEY;
const AWS_BUCKET_NAME = SECRETS.AWS_BUCKET_NAME;
const AWS_S3_REGION = SECRETS.AWS_S3_REGION;
const EXPIRY = 600; //10min 600s

AWS.config.update({
  accessKeyId: AWS_ACCESS_KEY_ID,
  secretAccessKey: AWS_SECRET_ACCESS_KEY,
  region: AWS_S3_REGION,
});
const s3 = new AWS.S3();

export const multerUpload = (bucketName, baseUrl) =>
  multer({
    storage: multerS3({
      s3: s3,
      bucket: bucketName,
      metadata: function (req, file, cb) {
        cb(null, { fieldName: file.fieldname });
      },
      // key: function (req, file, cb) {
      //   const fileName = file.originalname.replace(/[^a-zA-Z0-9._-]/g, "-"); // Replace special characters with hyphens ('-')
      //   cb(
      //     null,
      //     `${baseUrl}/${fileName}-${Date.now()}.${file.originalname
      //       .split(".")
      //       .pop()
      //       .toLowerCase()}`
      //   );
      // },
      key: function (req, file, cb) {
        const fileName = file.originalname.replace(/[^a-zA-Z0-9._-]/g, "-");
        cb(null, `${baseUrl}/${fileName}`);
      }
    }),
  });

export const downloadFile = async (req, res) => {
  try {
    if (req.body.location) {
      const DBFileLocation = req.body.location; //file location with extension
      const chunk = DBFileLocation.split("amazonaws.com/");
      const name = chunk.pop();
      const downloadUrl = s3.getSignedUrl("getObject", {
        Bucket: AWS_BUCKET_NAME,
        Key: name,
        Expires: EXPIRY, // expires in 1 hour
      });

      return res.status(200).json({
        url: downloadUrl,
        expiry: `${EXPIRY}s`,
      });
    } else {
      res.status(400).json({
        error: "Url not found",
      });
    }
  } catch (err) {
    res.status(500).json({
      error: err.message || "Internal Server error",
    });
  }
};
