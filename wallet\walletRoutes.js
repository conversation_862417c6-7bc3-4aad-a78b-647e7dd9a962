import { Router } from "express";
import {
  createWallet,
  deleteWallet,
  getAllWallets,
  getWalletById,
  updateWalletStatus,
} from "./walletController.js";
import { playerProtect } from "../utils/auth.js";
import authenticateUserAccess from "../Helpers/authenticationUser.js";

const router = Router();

router
  .route("/")
  .get(authenticateUserAccess("wallet", "read"), playerProtect, getAllWallets)
  .post(authenticateUserAccess("wallet", "write"), createWallet);
router
  .route("/:id")
  .get(authenticateUserAccess("wallet", "read"), getWalletById)
  .patch(authenticateUserAccess("wallet", "write"), updateWalletStatus)
  .delete(authenticateUserAccess("wallet", "delete"), deleteWallet);

export default router;
