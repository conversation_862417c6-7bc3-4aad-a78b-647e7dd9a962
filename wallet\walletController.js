import validator from "validator";
import { Wallet } from "./walletModal.js";
import { createTransaction } from "./transactions/transactionController.js";

// API's
export const getAllWallets = async (req, res) => {
  try {
    if (!req.auth) {
      return res.status(400).json({ error: "Access not allowed" });
    }
    let { email, page } = req.query;
    page = page || 1;
    let limitVal = 25;
    let skipValue = (page - 1) * limitVal;
    let query = {};
    if (email) {
      query.email = { $regex: email, $options: "i" };
    }
    const totalResults = await Wallet.countDocuments(query);
    const data = await Wallet.find(query)
      .skip(skipValue)
      .limit(limitVal)
      .populate(["playerId"])
      .sort({ createdAt: -1 });
    return res.status(200).json({ data, totalResults });
  } catch (e) {
    return res.status(500).json({ error: "Internal server error" });
  }
};

export const getWalletById = async (req, res) => {
  try {
    if (!req.auth) {
      return res.status(400).json({ error: "Access not allowed" });
    }
    const id = req.params.id;
    if (!id) {
      return res.status(400).json({ error: "Id is required" });
    }
    const data = await Wallet.findById(id).populate(["playerId"]);
    if (!data) {
      return res.status(400).json({ error: "Wallet Not found" });
    }
    return res.status(200).json(data);
  } catch (e) {
    return res.status(500).json({ error: "Internal server error" });
  }
};

export const createWallet = async (req, res) => {
  try {
    if (!req.auth) {
      return res.status(400).json({ error: "Access not allowed" });
    }
    const { email, playerId } = req.body;
    // Validate email if provided
    if (email && !validator.isEmail(email)) {
      return res.status(400).json({ error: "Invalid email address" });
    }
    if (!playerId) {
      return res.status(400).json({ error: "player id is required" });
    }
    const wallet = await Wallet.findOne({ email });
    if (wallet) {
      return res.status(400).json({ error: "User wallet already exists" });
    } else {
      const data = await Wallet.create({ email, playerId });
      return res.status(200).json(data);
    }
  } catch (e) {
    return res.status(500).json({ error: "Internal server error" });
  }
};

export const deleteWallet = async (req, res) => {
  try {
    if (!req.auth) {
      return res.status(400).json({ error: "Access not allowed" });
    }
    const id = req.params.id;
    if (!id) {
      return res.status(400).json({ error: "Id is required" });
    }
    const wallet = await Wallet.findByIdAndDelete(id);
    if (wallet) {
      return res.status(200).json({ message: "Wallet deleted SuccessFully" });
    } else {
      return res.statue(500).json({ error: "wallet not found" });
    }
  } catch (e) {
    return res.status(500).json({ error: "Internal server error" });
  }
};

export const updateWalletStatus = async (req, res) => {
  try {
    if (!req.auth) {
      return res.status(400).json({ error: "Access not allowed" });
    }
    const id = req.params.id;
    if (!id) {
      return res.status(400).json({ error: "Id is required" });
    }
    const { status } = req.body;
    const data = await Wallet.findByIdAndUpdate(id, { status }, { new: true });
    if (data) {
      return res.status(200).json(data);
    } else {
      return res.status(400).json({ error: "Wallet Not found" });
    }
  } catch (e) {
    return res.status(500).json({ error: "Internal server Error" });
  }
};

// Functions
export const createPlayerWallet = async (email, playerId) => {
  try {
    // Validate email if provided
    if (email && !validator.isEmail(email)) {
      return {
        status: 400,
        error: "Invalid email address",
      };
    }
    if (!playerId) {
      return {
        status: 400,
        error: "Player id is required",
      };
    }
    const wallet = await Wallet.findOne({ email });
    if (wallet) {
      return {
        status: 400,
        error: "User wallet already exists",
      };
    } else {
      const data = await Wallet.create({ email, playerId });
      return {
        status: 200,
        data,
      };
    }
  } catch (e) {
    return {
      status: 400,
      error: "Internal server error",
    };
  }
};

export const updateBalance = async (
  playerId,
  type,
  courseId,
  email,
  amount
) => {
  try {
    const currentWallet = await Wallet.findOne({ playerId });
    // Update wallet balance
    const updatedWallet = await Wallet.findOneAndUpdate(
      { playerId },
      {
        $set: { previous: currentWallet.balance }, // Set previous amount to current balance
        $inc: { balance: Math.ceil(amount) }, // Set balance to the provided amount
      },
      { new: true }
    );

    // Create transaction record
    const transactionParams = {
      email,
      playerId,
      type,
      amount: Math.abs(Math.ceil(amount)), // Ensure the amount is positive
      walletId: updatedWallet._id,
      courseId,
    };

    const transactionResult = await createTransaction(transactionParams);

    return {
      status: 200,
      message: "Wallet balance updated successfully",
      data: transactionResult.data,
    };
  } catch (e) {
    console.error(e);
    return {
      status: 500,
      error: "Transaction not created",
    };
  }
};
