import { SECRETS } from "../../utils/config.js";
import UserGroup from "./userGroupModal.js";

const allowedKeys = [
  "coach",
  "course",
  "category",
  "booking",
  "cms",
  "player",
  "user",
  "user_group",
  "contact",
  "notify",
  "news",
  "reports",
  "academy",

];

export const getUserGroupByID = async (req, res) => {
  try {
    if (!req.auth) {
      return res.status(400).json({ error: "Access not allowed" });
    }
    const userGroup = await UserGroup.findById(req.params.id);
    return res.status(200).json(userGroup);
  } catch (error) {
    return res.status(500).json({ err: error.message });
  }
};

export const createUserGroup = async (req, res) => {
  try {
    if (!req.auth) {
      return res.status(400).json({ error: "Access not allowed" });
    }
    let validate = validateAccessScopes(req.body.access_scopes);
    if (validate) {
      return res.status(400).json(validate);
    }
    const userGroup = new UserGroup(req.body);
    const savedUserGroup = await userGroup.save();
    return res.status(201).json(savedUserGroup);
  } catch (error) {
    return res.status(400).json({ err: error.message });
  }
};

export const getAllUserGroups = async (req, res) => {
  try {
    if (!req.auth) {
      return res.status(400).json({ error: "Access not allowed" });
    }
    const userGroups = await UserGroup.find({});
    return res.json(userGroups);
  } catch (error) {
    return res.status(500).json({ err: error.message });
  }
};

export const updateUserGroupById = async (req, res) => {
  try {
    if (!req.auth) {
      return res.status(400).json({ error: "Access not allowed" });
    }
    if (req.params.id === SECRETS.superAdmin)
      throw new Error("Can't edit this ");
    if (req.body.access_scopes) {
      let validate = validateAccessScopes(req.body.access_scopes);
      if (validate) {
        return res.status(400).json(validate);
      }
    }
    const userGroup = await UserGroup.findByIdAndUpdate(
      req.params.id,
      req.body,
      {
        new: true,
        runValidators: true,
      }
    );
    if (!userGroup) {
      return res.status(404).json({ err: "UserGroup not found" });
    }
    return res.json(userGroup);
  } catch (error) {
    return res.status(400).json({ err: error.message });
  }
};

export const deleteUserGroup = async (req, res) => {
  try {
    if (!req.auth) {
      return res.status(400).json({ error: "Access not allowed" });
    }
    if (req.params.id === SECRETS.superAdmin)
      throw new Error("Can't delete this ");
    const userGroup = await UserGroup.findByIdAndDelete(req.params.id);
    if (!userGroup) {
      return res.status(404).json({ err: "UserGroup not found" });
    }
    return res.json({ message: "UserGroup deleted successfully" });
  } catch (error) {
    return res.status(500).json({ err: error.message });
  }
};

function validateAccessScopes(access_scopes) {
  if (!access_scopes) {
    return { err: "Role must have at least one scope" };
  }
  let providedKeys = Object.keys(access_scopes);
  let isAnyOtherKeyPresent = providedKeys.filter(
    (key) => !allowedKeys.includes(key)
  );

  if (isAnyOtherKeyPresent.length) {
    return { err: `${isAnyOtherKeyPresent[0]} is not allowed` };
  }

  let oneSelected = true;
  for (let i = 0; i < providedKeys.length; i++) {
    if (access_scopes[providedKeys[i]].length) {
      oneSelected = false;
      break;
    }
  }
  if (oneSelected) {
    return { err: "Role must have at least one scope" };
  }
}
