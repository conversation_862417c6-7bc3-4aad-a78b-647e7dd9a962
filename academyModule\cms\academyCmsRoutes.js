import { Router } from "express";
import {
  getAcademyFacilities,
  toggleAcademyFacilityStatus,
  getActiveAcademyFacilities,
  updateAcademyFacilityPosition,
  addTopCoach,
  getTopCoaches,
  updateCoachPosition,
  addTopCourse,
  getTopCourses,
  updateCoursePosition,
  getAcademyDescription,
  postAcademyDescription,
  getAcademyBlocks,
  getAcademyBlocksDataAdmin,
  createAcademyBlock,
  updateAcademyBlock,
  updateAcademyBlockPosition,
  getAcademyTestimonials,
  createAcademyTestimonials,
  updateAcademyTestimonials,
  updateAcademyTestimonialsPosition,
  deleteAcademyTestimonials,
} from "./academyCmsController.js";
import { academyUserProtect } from "../../utils/auth.js";
import { checkAcademyUserAccess } from "../utils/auth.js";
import {
  addTopCoachSchema,
  updateCoachPositionSchema,
  addTopCourseSchema,
  updateCoursePositionSchema,
  academyDescriptionSchema,
  addAcademyTestimonialsSchema,
  updateAcademyTestimonialsSchema,
  updateTestimonialPositionSchema,
  writeAcademyBlockSchema,
} from "./academyCmsValidation.js";
import { validateRequest } from "../../middlewares/validations.js";

const router = Router();

//Blocks
router.get(
  "/admin/blocks",
  academyUserProtect(),
  checkAcademyUserAccess("cms", "read"),
  getAcademyBlocksDataAdmin
);
router.get("/:academyId/blocks", getAcademyBlocks);

router.post(
  "/create/block",
  academyUserProtect(),
  checkAcademyUserAccess("cms", "write"),
  validateRequest(writeAcademyBlockSchema),
  createAcademyBlock
);
router.patch(
  "/update/block/position",
  academyUserProtect(),
  checkAcademyUserAccess("cms", "write"),
  updateAcademyBlockPosition
);
router.patch(
  "/update/block/:id",
  academyUserProtect(),
  checkAcademyUserAccess("cms", "write"),
  validateRequest(writeAcademyBlockSchema),
  updateAcademyBlock
);

//Facilities
router.get(
  "/facilities",
  academyUserProtect(),
  checkAcademyUserAccess("cms", "read"),
  getAcademyFacilities
);
router.get("/:academyId/facilities/active", getActiveAcademyFacilities);
router.patch(
  "/facilities/:facilityId/toggle-status",
  academyUserProtect(),
  checkAcademyUserAccess("cms", "write"),
  toggleAcademyFacilityStatus
);
router.patch(
  "/facilities/position",
  academyUserProtect(),
  checkAcademyUserAccess("cms", "write"),
  updateAcademyFacilityPosition
);

//Top Coach
router.get(
  "/:academyId/coach",
  academyUserProtect(true),
  checkAcademyUserAccess("cms", "read", true),
  getTopCoaches
);
router.post(
  "/coach/add",
  academyUserProtect(),
  checkAcademyUserAccess("cms", "write"),
  validateRequest(addTopCoachSchema),
  addTopCoach
);
router.patch(
  "/coach/position",
  academyUserProtect(),
  checkAcademyUserAccess("cms", "write"),
  validateRequest(updateCoachPositionSchema),
  updateCoachPosition
);

//Course

router.post(
  "/course/add",
  academyUserProtect(),
  checkAcademyUserAccess("cms", "write"),
  validateRequest(addTopCourseSchema),
  addTopCourse
);

router.get(
  "/:academyId/course",
  academyUserProtect(true),
  checkAcademyUserAccess("cms", "read", true),
  getTopCourses
);

router.patch(
  "/course/position",
  academyUserProtect(),
  checkAcademyUserAccess("cms", "write"),
  validateRequest(updateCoursePositionSchema),
  updateCoursePosition
);

//Description

router.get(
  "/:academyId/description",
  academyUserProtect(true),
  checkAcademyUserAccess("cms", "read", true),
  getAcademyDescription
);

router.post(
  "/description",
  academyUserProtect(),
  checkAcademyUserAccess("cms", "write"),
  validateRequest(academyDescriptionSchema),
  postAcademyDescription
);

// Testimonials

router.get(
  "/:academyId/testimonials",
  academyUserProtect(true),
  checkAcademyUserAccess("cms", "read", true),
  getAcademyTestimonials
);

router.post(
  "/create/testimonial",
  academyUserProtect(),
  checkAcademyUserAccess("cms", "write"),
  validateRequest(addAcademyTestimonialsSchema),
  createAcademyTestimonials
);

router.patch(
  "/update/testimonial/position",
  academyUserProtect(),
  checkAcademyUserAccess("cms", "write"),
  validateRequest(updateTestimonialPositionSchema),
  updateAcademyTestimonialsPosition
);

router.patch(
  "/update/testimonial/:id",
  academyUserProtect(),
  checkAcademyUserAccess("cms", "write"),
  validateRequest(updateAcademyTestimonialsSchema),
  updateAcademyTestimonials
);

router.delete(
  "/delete/testimonial/:id",
  academyUserProtect(),
  checkAcademyUserAccess("cms", "delete"),
  deleteAcademyTestimonials
);

export default router;
