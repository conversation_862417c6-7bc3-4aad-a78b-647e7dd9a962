import { Router } from "express";

import {
  createCategory,
  deleteCategory,
  getCategories,
  getCategoryById,
  updateCategory,
  uploadFile,
} from "./categoriesController.js";
import authenticateUserAccess from "../Helpers/authenticationUser.js";

const router = Router();

router
  .route("/")
  .get(authenticateUserAccess("category", "read"), getCategories)
  .post(authenticateUserAccess("category", "write"), createCategory);
router
  .route("/:id")
  .get(authenticateUserAccess("category", "read"), getCategoryById)
  .patch(authenticateUserAccess("category", "write"), updateCategory)
  .delete(authenticateUserAccess("category", "delete"), deleteCategory);
router
  .route("/uploadImage/")
  .post(authenticateUserAccess("category", "write"), uploadFile);

export default router;
