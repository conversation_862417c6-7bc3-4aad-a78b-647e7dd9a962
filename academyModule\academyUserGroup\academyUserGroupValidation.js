import Joi from "joi";

export const createAcademyUserGroupSchema = Joi.object({
  name: Joi.string().min(3).required(),
  description: Joi.string().optional(),
  current_user: Joi.optional(),
  access_scopes: Joi.object({
    dashboard: Joi.array()
      .items(Joi.string().valid("read", "write", "delete"))
      .default([]),
    coach: Joi.array()
      .items(Joi.string().valid("read", "write", "delete"))
      .default([]),
    course: Joi.array()
      .items(Joi.string().valid("read", "write", "delete"))
      .default([]),
    user: Joi.array()
      .items(Joi.string().valid("read", "write", "delete"))
      .default([]),
    user_group: Joi.array()
      .items(Joi.string().valid("read", "write", "delete"))
      .default([]),
    finances: Joi.array()
      .items(Joi.string().valid("read", "write", "delete"))
      .default([]),
    booking: Joi.array()
      .items(Joi.string().valid("read", "write", "delete"))
      .default([]),
    reports: Joi.array()
      .items(Joi.string().valid("read", "write", "delete"))
      .default([]),
    player: Joi.array()
      .items(Joi.string().valid("read", "write", "delete"))
      .default([]),
    cms: Joi.array()
      .items(Joi.string().valid("read", "write", "delete"))
      .default([]),
    contact: Joi.array()
      .items(Joi.string().valid("read", "write", "delete"))
      .default([]),
  })
    .required()
    .custom((value, helpers) => {
      // Check if at least one module has permissions
      const hasAnyPermission = Object.values(value).some(
        (permissions) => Array.isArray(permissions) && permissions.length > 0
      );

      if (!hasAnyPermission) {
        return helpers.error("any.invalid", {
          message: "At least one module must have permissions assigned",
        });
      }

      return value;
    }),
});

export const updateAcademyUserGroupSchema = Joi.object({
  name: Joi.string().min(3).optional(),
  description: Joi.string().optional(),
  current_user: Joi.optional(),
  password: Joi.string().min(6).optional(),
  access_scopes: Joi.object({
    dashboard: Joi.array()
      .items(Joi.string().valid("read", "write", "delete"))
      .default([]),
    coach: Joi.array()
      .items(Joi.string().valid("read", "write", "delete"))
      .default([]),
    course: Joi.array()
      .items(Joi.string().valid("read", "write", "delete"))
      .default([]),
    user: Joi.array()
      .items(Joi.string().valid("read", "write", "delete"))
      .default([]),
    user_group: Joi.array()
      .items(Joi.string().valid("read", "write", "delete"))
      .default([]),
    finances: Joi.array()
      .items(Joi.string().valid("read", "write", "delete"))
      .default([]),
    booking: Joi.array()
      .items(Joi.string().valid("read", "write", "delete"))
      .default([]),
    reports: Joi.array()
      .items(Joi.string().valid("read", "write", "delete"))
      .default([]),
    player: Joi.array()
      .items(Joi.string().valid("read", "write", "delete"))
      .default([]),
    cms: Joi.array()
      .items(Joi.string().valid("read", "write", "delete"))
      .default([]),
    contact: Joi.array()
      .items(Joi.string().valid("read", "write", "delete"))

      .default([])
  })
    .required()
    .custom((value, helpers) => {
      // Check if at least one module has permissions
      const hasAnyPermission = Object.values(value).some(
        (permissions) => Array.isArray(permissions) && permissions.length > 0
      );

      if (!hasAnyPermission) {
        return helpers.error("any.invalid", {
          message: "At least one module must have permissions assigned",
        });
      }

      return value;
    }),
});
