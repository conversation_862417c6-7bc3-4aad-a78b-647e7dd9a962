import mongoose from "mongoose";
const { Schema, model } = mongoose;

const notifySchema = new Schema(
  {
    email: {
      type: String,
      required: true,
    },
    message: {
      type: String,
      required: true,
    },
    status: {
      type: String,
      default: "active",
      enum: ["active", "inactive"],
    },
  },
  { timestamps: true }
);

export const Notify = model("notify", notifySchema);
