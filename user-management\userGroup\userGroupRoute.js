import express from "express";
import authenticateUserAccess from "../../Helpers/authenticationUser.js";
import { createUserGroup, deleteUserGroup, getAllUserGroups, getUserGroupByID, updateUserGroupById } from "./userGrouptController.js";
const UserGroupRoute = express.Router();

UserGroupRoute.get(
  "/:id",
  authenticateUserAccess("user_group", "read"),
  getUserGroupByID
);
UserGroupRoute.post(
  "/",
  authenticateUserAccess("user_group", "write"),
  createUserGroup
);
UserGroupRoute.get(
  "/",
  authenticateUserAccess("user_group", "read"),
  getAllUserGroups
);
UserGroupRoute.put(
  "/:id",
  authenticateUserAccess("user_group", "write"),
  updateUserGroupById
);
UserGroupRoute.delete(
  "/:id",
  authenticateUserAccess("user_group", "delete"),
  deleteUserGroup
);

export default UserGroupRoute;