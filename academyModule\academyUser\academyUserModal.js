import mongoose from "mongoose";

const AcademyUserSchema = new mongoose.Schema(
  {
    academyId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "academy",
      required: true,
    },
    name: {
      type: String,
      required: [true, "Name required"],
      validate: {
        validator: function (name) {
          return name.trim().length > 2;
        },
        message: "Name should be of at least 3 characters",
      },
    },
    email: {
      type: String,
      required: [true, "Email required"],
      unique: [true, "Email ID already exists"],
      validate: {
        validator: function (email) {
          return email.trim().length > 5;
        },
        message: "Email should be of at least 6 characters",
      },
    },
    password: {
      type: String,
      required: true,
    },
    isDefaultAdmin: {
      type: Boolean,
      default: false,
    },
    academyUserGroups: [
      { type: mongoose.Schema.Types.ObjectId, ref: "AcademyUserGroup" },
    ],
    refreshToken: {
      // required: true,
      type: String,
    },
    resetPasswordToken: {
      type: String,
    },
    resetPasswordExpires: {
      type: Date,
    },
  },
  { timestamps: true }
);

const AcademyUser = mongoose.model("AcademyUser", AcademyUserSchema);

export default AcademyUser;
