import express from "express";
import playerRouter from "./player/playerRoutes.js";
import coachRouter from "./coaches/coachRoutes.js";
import cmsRoutes from "./cms/cmsRoutes.js";
import calendarRouter from "./calendar/calendarRoutes.js";
import categoryRouter from "./categories/categoriesRoutes.js";
import courseRouter from "./courses/courseRoutes.js";
import BookingRouter from "./bookings/bookingRoutes.js";
import EventRouter from "./events/eventRoutes.js";
import WalletRouter from "./wallet/walletRoutes.js";
import TransactionsRouter from "./wallet/transactions/transactionRoutes.js";
import NewsletterRouter from "./newsletter/newsRoutes.js";
import ContactUsRouter from "./contactUs/contactRoutes.js";
import NotifyRouter from "./courseNotify/courseNotifyRoutes.js";
import RazorpayRouter from "./razorpay/razorpayRoutes.js";
import userRouter from "./user-management/User/userRoutes.js";
import UserGroupRoute from "./user-management/userGroup/userGroupRoute.js";

import academyRouter from "./academyModule/academy/academyRoutes.js";
import academyUserRouter from "./academyModule/academyUser/academyUserRoutes.js";
import academyUserGroupRouter from "./academyModule/academyUserGroup/academyUserGroupRoutes.js";
import academyCmsRouter from "./academyModule/cms/academyCmsRoutes.js";

const router = express.Router();

router.use("/academy", academyRouter);
router.use("/academy-users", academyUserRouter);
router.use("/academy-user-groups", academyUserGroupRouter);
router.use("/academy-cms" , academyCmsRouter);
router.use("/coach", coachRouter);
router.use("/calendar", calendarRouter);
router.use("/player", playerRouter);
router.use("/category", categoryRouter);
router.use("/course", courseRouter);
router.use("/cms", cmsRoutes);
router.use("/user", userRouter);
router.use("/userGroup", UserGroupRoute);
router.use("/booking", BookingRouter);
router.use("/payments", RazorpayRouter);
router.use("/events", EventRouter);
router.use("/wallet", WalletRouter);
router.use("/transactions", TransactionsRouter);
router.use("/newsletter", NewsletterRouter);
router.use("/contactUs", ContactUsRouter);
router.use("/courseNotify", NotifyRouter);

export default router;
