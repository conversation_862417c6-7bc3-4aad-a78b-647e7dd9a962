import { BlockCms } from "./Model/blocksModal.js";
import { Faqs } from "./Model/faqModal.js";
import { Policy } from "./Model/policyModal.js";
import { RegistrationInstructor } from "./Model/registrationInstructor.js";
import { Testimonial } from "./Model/testimonials.js";
import { TopCoach } from "./Model/topCoachModal.js";
import { TopCourse } from "./Model/topCourseModal.js";
import { TopAcademy } from "./Model/topAcademyModal.js";
import mongoose from "mongoose";
import { multerUpload } from "../utils/awsHelper.js";
import { SECRETS } from "../utils/config.js";
import AWS from "aws-sdk";
import { TopCategory } from "./Model/topCategoryModal.js";
import { CmsAboutUs } from "./Model/aboutUsCmsModal.js";
import { CmsRegistrationDetails } from "./Model/registrationDetailCmsModal.js";
import TermsAndCondition from "./Model/termsAndConditionModal.js";
import PlayerTermsAndConditionModal from "./Model/playerTermsAndConditionModal.js";

const AWS_ACCESS_KEY_ID = SECRETS.AWS_ACCESS_KEY_ID;
const AWS_SECRET_ACCESS_KEY = SECRETS.AWS_SECRET_ACCESS_KEY;
const AWS_BUCKET_NAME = SECRETS.AWS_BUCKET_NAME;
const AWS_S3_REGION = SECRETS.AWS_S3_REGION;

AWS.config.update({
  accessKeyId: AWS_ACCESS_KEY_ID,
  secretAccessKey: AWS_SECRET_ACCESS_KEY,
  region: AWS_S3_REGION,
});
const s3 = new AWS.S3();

// CMS Upload File
export const uploadFile = async (req, res) => {
  try {
    if (!req.auth) {
      return res.status(400).json({ error: "Access not allowed" });
    }
    const uploadSingle = multerUpload(AWS_BUCKET_NAME, "images2/cms").single(
      "image"
    );

    uploadSingle(req, res, async (err) => {
      if (err) {
        return res.status(400).json({ error: err.message });
      }
      const url = req.body.url;
      if (url) {
        const deleteParams = {
          Bucket: AWS_BUCKET_NAME,
          Key: `images2/cms/${url.split("/").pop()}`,
        };
        await s3.deleteObject(deleteParams).promise();
        return res.status(200).json({ result: "Image deleted" });
      }
      // The uploaded file URL directly from S3
      const imageUrl = req?.file?.location;

      return res.status(200).json({ url: imageUrl });
    });
  } catch (error) {
    res.status(500).json({
      error: error.message || "Internal server error",
    });
  }
};

// ### Top Academy ### //
export const getTopAcademies = async (req, res) => {
  try {
    const academies = await TopAcademy.find().populate("academy");
    return res.status(200).json(academies);
  } catch (e) {
    console.log(e);
    return res.status(500).json({ error: "Internal server error" });
  }
};

export const addTopAcademy = async (req, res) => {
  try {
    if (!req.auth) {
      return res.status(400).json({ error: "Access not allowed" });
    }
    const { documents } = req.body;
    if (documents.length > 15) {
      return res
        .status(400)
        .json({ message: "Top Academy cannot be greater than 15" });
    }
    await TopAcademy.deleteMany({});
    const academies = await TopAcademy.insertMany(documents);
    return res
      .status(200)
      .json({ message: "Academy Updated Successfully...", result: academies });
  } catch (e) {
    console.log(e);
    return res.status(500).json({ error: "Internal server error" });
  }
};

export const updateAcademyPosition = async (req, res) => {
  try {
    if (!req.auth) {
      return res.status(400).json({ error: "Access not allowed" });
    }
    const { updates } = req.body;
    const updatePromises = updates.map(({ id, newPosition }) => {
      return TopAcademy.findByIdAndUpdate(
        id,
        { $set: { position: Number(newPosition) } },
        { new: true }
      );
    });

    Promise.all(updatePromises)
      .then((results) => {
        res.status(200).json({ result: "Position updated", status: "SUCCESS" });
      })
      .catch((error) => {
        res
          .status(400)
          .json({ error: "Error updating documents", message: error });
      });
  } catch (e) {
    console.log(e);
    res.status(500).send("Internal Server Error");
  }
};

// ### Top Course ### //
export const getTopCourse = async (req, res) => {
  try {
    const topCourses = await TopCourse.find().populate("course");
    return res.status(200).json(topCourses);
  } catch (e) {
    console.log(e);
    return res.status(500).json({ error: "Internal server error" });
  }
};

export const addTopCourse = async (req, res) => {
  try {
    if (!req.auth) {
      return res.status(400).json({ error: "Access not allowed" });
    }
    const { documents } = req.body;
    if (documents.length > 15) {
      return res
        .status(400)
        .json({ message: "Top Course cannot be greater than 15" });
    }
    await TopCourse.deleteMany({});
    const coach = await TopCourse.insertMany(documents);
    return res
      .status(200)
      .json({ message: "Course Updated Successfully...", result: coach });
  } catch (e) {
    console.log(e);
    return res.status(500).json({ error: "Internal server error" });
  }
};

export const updateCoursePosition = async (req, res) => {
  try {
    if (!req.auth) {
      return res.status(400).json({ error: "Access not allowed" });
    }
    const { updates } = req.body;
    const updatePromises = updates.map(({ id, newPosition }) => {
      return TopCourse.findByIdAndUpdate(
        id,
        { $set: { position: Number(newPosition) } },
        { new: true }
      );
    });

    Promise.all(updatePromises)
      .then((results) => {
        res.status(200).json({ result: "Position updated", status: "SUCCESS" });
      })
      .catch((error) => {
        res
          .status(400)
          .json({ error: "Error updating documents", message: error });
      });
  } catch (e) {
    console.log(e);
    res.status(500).send("Internal Server Error");
  }
};

// ### Top Coach ### //
export const getTopCoaches = async (req, res) => {
  try {
    const coach = await TopCoach.find().populate("coach");
    return res.status(200).json(coach);
  } catch (e) {
    console.log(e);
    return res.status(500).json({ error: "Internal server error" });
  }
};

export const addTopCoach = async (req, res) => {
  try {
    if (!req.auth) {
      return res.status(400).json({ error: "Access not allowed" });
    }
    const { documents } = req.body;
    if (documents.length > 15) {
      return res
        .status(400)
        .json({ message: "Top Coach cannot be greater than 15" });
    }
    await TopCoach.deleteMany({});
    const coach = await TopCoach.insertMany(documents);
    return res
      .status(200)
      .json({ message: "Coach Updated Successfully...", result: coach });
  } catch (e) {
    console.log(e);
    return res.status(500).json({ error: "Internal server error" });
  }
};

export const updateCoachPosition = async (req, res) => {
  try {
    if (!req.auth) {
      return res.status(400).json({ error: "Access not allowed" });
    }
    const { updates } = req.body;
    const updatePromises = updates.map(({ id, newPosition }) => {
      return TopCoach.findByIdAndUpdate(
        id,
        { $set: { position: Number(newPosition) } },
        { new: true }
      );
    });

    Promise.all(updatePromises)
      .then((results) => {
        res.status(200).json({ result: "Position updated", status: "SUCCESS" });
      })
      .catch((error) => {
        res
          .status(400)
          .json({ error: "Error updating documents", message: error });
      });
  } catch (e) {
    console.log(e);
    res.status(500).send("Internal Server Error");
  }
};

// ### Top Categories ### //
export const getTopCategories = async (req, res) => {
  try {
    const category = await TopCategory.find().populate("category");
    return res.status(200).json(category);
  } catch (e) {
    console.log(e);
    return res.status(500).json({ error: "Internal server error" });
  }
};

export const addTopCategories = async (req, res) => {
  try {
    if (!req.auth) {
      return res.status(400).json({ error: "Access not allowed" });
    }
    const { documents } = req.body;
    if (documents.length > 15) {
      return res
        .status(400)
        .json({ message: "Top Category cannot be greater than 15" });
    }
    await TopCategory.deleteMany({});
    const data = await TopCategory.insertMany(documents);
    return res
      .status(200)
      .json({ message: "Coach Updated Successfully...", result: data });
  } catch (e) {
    console.log(e);
    return res.status(500).json({ error: "Internal server error" });
  }
};

export const updateCategoryPosition = async (req, res) => {
  try {
    if (!req.auth) {
      return res.status(400).json({ error: "Access not allowed" });
    }
    const { updates } = req.body;
    const updatePromises = updates.map(({ id, newPosition }) => {
      return TopCategory.findByIdAndUpdate(
        id,
        { $set: { position: Number(newPosition) } },
        { new: true }
      );
    });

    Promise.all(updatePromises)
      .then((results) => {
        res.status(200).json({ result: "Position updated", status: "SUCCESS" });
      })
      .catch((error) => {
        res
          .status(400)
          .json({ error: "Error updating documents", message: error });
      });
  } catch (e) {
    console.log(e);
    res.status(500).send("Internal Server Error");
  }
};

// ### FAQ ### //
export const getFaqs = async (req, res) => {
  try {
    const data = await Faqs.find();
    res.status(200).json(data);
  } catch (e) {
    console.log(e);
    res.status(500).json({ error: "Internal Server Error" });
  }
};

export const createFaq = async (req, res) => {
  try {
    if (!req.auth) {
      return res.status(400).json({ error: "Access not allowed" });
    }
    let addObj = {
      ...req.body,
    };
    const data = await Faqs.create(addObj);
    res.status(200).json(data);
  } catch (e) {
    console.log(e);
    res.status(500).json({ error: "Internal Server Error" });
  }
};

export const updateFaq = async (req, res) => {
  try {
    if (!req.auth) {
      return res.status(400).json({ error: "Access not allowed" });
    }
    const id = req.params.id;
    if (!id) {
      return res.status(400).json({ message: "Id not found" });
    }
    let updateObj = {
      ...req.body,
    };
    const data = await Faqs.findByIdAndUpdate(id, updateObj, {
      new: true,
    });
    res.status(200).json(data);
  } catch (e) {
    console.log(e);
    res.status(500).json({ error: "Internal Server Error" });
  }
};

export const getFaqById = async (req, res) => {
  try {
    const id = req.params.id;
    if (!id) {
      return res.status(400).json({ message: "Id not found" });
    }
    const data = await Faqs.findById(id);
    res.status(200).json(data);
  } catch (e) {
    console.log(e);
    res.status(500).json({ error: "Internal Server Error" });
  }
};

export const deleteFaq = async (req, res) => {
  try {
    if (!req.auth) {
      return res.status(400).json({ error: "Access not allowed" });
    }
    const id = req.params.id;
    if (!id) {
      return res.status(400).json({ message: "Id not found" });
    }
    const data = await Faqs.findByIdAndDelete(id);
    res.status(200).json(data);
  } catch (e) {
    console.log(e);
    res.status(500).json({ error: "Internal Server Error" });
  }
};

// ### Policy ### //
export const getPolicy = async (req, res) => {
  try {
    const data = await Policy.find();
    res.status(200).json(data);
  } catch (e) {
    console.log(e);
    res.status(500).json({ error: "Internal Server Error" });
  }
};

export const createPolicy = async (req, res) => {
  try {
    if (!req.auth) {
      return res.status(400).json({ error: "Access not allowed" });
    }
    let addObj = {
      ...req.body,
    };
    const data = await Policy.create(addObj);
    res.status(200).json(data);
  } catch (e) {
    console.log(e);
    res.status(500).json({ error: "Internal Server Error" });
  }
};

export const updatePolicy = async (req, res) => {
  try {
    if (!req.auth) {
      return res.status(400).json({ error: "Access not allowed" });
    }
    const id = req.params.id;
    if (!id) {
      return res.status(400).json({ message: "Id not found" });
    }
    let updateObj = {
      ...req.body,
    };
    const data = await Policy.findByIdAndUpdate(id, updateObj, {
      new: true,
    });
    res.status(200).json(data);
  } catch (e) {
    console.log(e);
    res.status(500).json({ error: "Internal Server Error" });
  }
};

// ### About Us ### //
export const getAboutUs = async (req, res) => {
  try {
    const data = await CmsAboutUs.find();
    res.status(200).json(data);
  } catch (e) {
    console.log(e);
    res.status(500).json({ error: "Internal Server Error" });
  }
};

export const createAboutUs = async (req, res) => {
  try {
    if (!req.auth) {
      return res.status(400).json({ error: "Access not allowed" });
    }
    let addObj = {
      ...req.body,
    };
    const data = await CmsAboutUs.create(addObj);
    res.status(200).json(data);
  } catch (e) {
    console.log(e);
    res.status(500).json({ error: "Internal Server Error" });
  }
};

export const updateAboutUs = async (req, res) => {
  try {
    if (!req.auth) {
      return res.status(400).json({ error: "Access not allowed" });
    }
    const id = req.params.id;
    if (!id) {
      return res.status(400).json({ message: "Id not found" });
    }
    let updateObj = {
      ...req.body,
    };
    const data = await CmsAboutUs.findByIdAndUpdate(id, updateObj, {
      new: true,
    });
    res.status(200).json(data);
  } catch (e) {
    console.log(e);
    res.status(500).json({ error: "Internal Server Error" });
  }
};

// ### About Us ### //
export const getRegistration = async (req, res) => {
  try {
    const data = await CmsRegistrationDetails.find();
    res.status(200).json(data);
  } catch (e) {
    console.log(e);
    res.status(500).json({ error: "Internal Server Error" });
  }
};

// Registration Details
export const createRegistration = async (req, res) => {
  try {
    if (!req.auth) {
      return res.status(400).json({ error: "Access not allowed" });
    }
    let addObj = {
      ...req.body,
    };
    const data = await CmsRegistrationDetails.create(addObj);
    res.status(200).json(data);
  } catch (e) {
    console.log(e);
    res.status(500).json({ error: "Internal Server Error" });
  }
};

export const updateRegistration = async (req, res) => {
  try {
    if (!req.auth) {
      return res.status(400).json({ error: "Access not allowed" });
    }
    const id = req.params.id;
    if (!id) {
      return res.status(400).json({ message: "Id not found" });
    }
    let updateObj = {
      ...req.body,
    };
    const data = await CmsRegistrationDetails.findByIdAndUpdate(id, updateObj, {
      new: true,
    });
    res.status(200).json(data);
  } catch (e) {
    console.log(e);
    res.status(500).json({ error: "Internal Server Error" });
  }
};

// Terms And Condition Details

export const getTermsAndCondition = async (req, res) => {
  try {
    const data = await TermsAndCondition.find();
    res.status(200).json(data);
  } catch (e) {
    console.log(e);
    res.status(500).json({ error: "Internal Server Error" });
  }
};

export const getPlayerTermsAndCondition = async (req, res) => {
  try {
    const data = await PlayerTermsAndConditionModal.find();
    res.status(200).json(data);
  } catch (e) {
    console.log(e);
    res.status(500).json({ error: "Internal Server Error" });
  }
};

export const createTermsAndCondition = async (req, res) => {
  try {
    if (!req.auth) {
      return res.status(400).json({ error: "Access not allowed" });
    }
    let addObj = {
      ...req.body,
    };
    const data = await TermsAndCondition.create(addObj);
    res.status(200).json(data);
  } catch (e) {
    console.log(e);
    res.status(500).json({ error: "Internal Server Error" });
  }
};

export const createPlayerTermsAndCondition = async (req, res) => {
  try {
    if (!req.auth) {
      return res.status(400).json({ error: "Access not allowed" });
    }
    let addObj = {
      ...req.body,
    };
    const data = await PlayerTermsAndConditionModal.create(addObj);
    res.status(200).json(data);
  } catch (e) {
    console.log(e);
    res.status(500).json({ error: "Internal Server Error" });
  }
};

export const updateTermsAndCondition = async (req, res) => {
  try {
    if (!req.auth) {
      return res.status(400).json({ error: "Access not allowed" });
    }
    const id = req.params.id;
    if (!id) {
      return res.status(400).json({ message: "Id not found" });
    }
    let updateObj = {
      ...req.body,
    };
    const data = await TermsAndCondition.findByIdAndUpdate(id, updateObj, {
      new: true,
    });
    res.status(200).json(data);
  } catch (e) {
    console.log(e);
    res.status(500).json({ error: "Internal Server Error" });
  }
};

export const updatePlayerTermsAndCondition = async (req, res) => {
  try {
    if (!req.auth) {
      return res.status(400).json({ error: "Access not allowed" });
    }
    const id = req.params.id;
    if (!id) {
      return res.status(400).json({ message: "Id not found" });
    }
    let updateObj = {
      ...req.body,
    };
    const data = await PlayerTermsAndConditionModal.findByIdAndUpdate(id, updateObj, {
      new: true,
    });
    res.status(200).json(data);
  } catch (e) {
    console.log(e);
    res.status(500).json({ error: "Internal Server Error" });
  }
};

// ### Registration Instructor ### //
export const getRegistrationInstructor = async (req, res) => {
  try {
    const data = await RegistrationInstructor.find();
    res.status(200).json(data);
  } catch (e) {
    console.log(e);
    res.status(500).json({ error: "Internal Server Error" });
  }
};

export const createRegistrationInstructor = async (req, res) => {
  try {
    if (!req.auth) {
      return res.status(400).json({ error: "Access not allowed" });
    }
    let addObj = {
      ...req.body,
    };
    const data = await RegistrationInstructor.create(addObj);
    res.status(200).json(data);
  } catch (e) {
    console.log(e);
    res.status(500).json({ error: "Internal Server Error" });
  }
};

export const updateRegistrationInstructor = async (req, res) => {
  try {
    if (!req.auth) {
      return res.status(400).json({ error: "Access not allowed" });
    }
    const id = req.params.id;
    if (!id) {
      return res.status(400).json({ message: "Id not found" });
    }
    let updateObj = {
      ...req.body,
    };
    const data = await RegistrationInstructor.findByIdAndUpdate(id, updateObj, {
      new: true,
    });
    res.status(200).json(data);
  } catch (e) {
    console.log(e);
    res.status(500).json({ error: "Internal Server Error" });
  }
};

export const deleteRegistrationInstructor = async (req, res) => {
  try {
    if (!req.auth) {
      return res.status(400).json({ error: "Access not allowed" });
    }
    const id = req.params.id;
    if (!id) {
      return res.status(400).json({ message: "Id not found" });
    }
    const data = await RegistrationInstructor.findByIdAndDelete(id);
    res.status(200).json(data);
  } catch (e) {
    console.log(e);
    res.status(500).json({ error: "Internal Server Error" });
  }
};

// ### Testimonials ### //
export const getTestimonials = async (req, res) => {
  try {
    const data = await Testimonial.find().sort({ position: 1 });
    res.status(200).json(data);
  } catch (e) {
    console.log(e);
    res.status(500).json({ error: "Internal Server Error" });
  }
};

export const createTestimonials = async (req, res) => {
  try {
    if (!req.auth) {
      return res.status(400).json({ error: "Access not allowed" });
    }
    // Check if the number of existing documents is less than 15
    const count = await Testimonial.countDocuments();
    if (count > 15) {
      return res
        .status(400)
        .json({ message: "Maximum limit reached, cannot be grater than 15" });
    }

    let addObj = {
      ...req.body,
    };
    const data = await Testimonial.create(addObj);
    res.status(200).json(data);
  } catch (e) {
    console.log(e);
    res.status(500).json({ error: "Internal Server Error" });
  }
};

export const updateTestimonials = async (req, res) => {
  try {
    if (!req.auth) {
      return res.status(400).json({ error: "Access not allowed" });
    }
    const id = req.params.id;
    if (!id) {
      return res.status(400).json({ message: "Id not found" });
    }
    let updateObj = {
      ...req.body,
    };
    const data = await Testimonial.findByIdAndUpdate(id, updateObj, {
      new: true,
    });
    res.status(200).json(data);
  } catch (e) {
    console.log(e);
    res.status(500).json({ error: "Internal Server Error" });
  }
};

export const deleteTestimonials = async (req, res) => {
  try {
    if (!req.auth) {
      return res.status(400).json({ error: "Access not allowed" });
    }
    const id = req.params.id;
    if (!id) {
      return res.status(400).json({ message: "Id not found" });
    }
    const data = await Testimonial.findByIdAndDelete(id);
    // Update positions of remaining testimonials
    const remainingTestimonials = await Testimonial.find().sort({
      position: 1,
    });
    for (let i = 0; i < remainingTestimonials.length; i++) {
      remainingTestimonials[i].position = i + 1;
      await remainingTestimonials[i].save();
    }
    res.status(200).json(data);
  } catch (e) {
    console.log(e);
    res.status(500).json({ error: "Internal Server Error" });
  }
};

export const updateTestimonialPosition = async (req, res) => {
  try {
    if (!req.auth) {
      return res.status(400).json({ error: "Access not allowed" });
    }
    const { updates } = req.body;
    const updatePromises = updates.map(({ id, newPosition }) => {
      return Testimonial.findByIdAndUpdate(
        id,
        { $set: { position: Number(newPosition) } },
        { new: true }
      );
    });

    Promise.all(updatePromises)
      .then((results) => {
        res.status(200).json({ result: "Position updated", status: "SUCCESS" });
      })
      .catch((error) => {
        res
          .status(400)
          .json({ error: "Error updating documents", message: error });
      });
  } catch (e) {
    console.log(e);
    res.status(500).send("Internal Server Error");
  }
};

// ### Block ### //
export const getBlocks = async (req, res) => {
  try {
    const blockCms = await BlockCms.find({ visibility: true });
    const blocksWithReferencedData = await Promise.all(
      blockCms.map(async (block) => {
        const referencedCollectionName = block.collectionName;

        if (!referencedCollectionName) {
          return {
            blockData: block,
            referencedData: null, // Set referencedData to null or any other appropriate value
          };
        }

        // Check if the referenced model name is valid
        if (!mongoose.connection.models[referencedCollectionName]) {
          throw new Error(
            `Referenced model "${referencedCollectionName}" not found`
          );
        }

        const ReferencedModel = mongoose.model(referencedCollectionName);
        let dataFromReferencedCollection;
        if (referencedCollectionName === "topCourseCms") {
          const select = "_id coachName courseName description images fees";
          dataFromReferencedCollection = await ReferencedModel.find(
            {}
          ).populate({ path: "course", select });
        } else if (referencedCollectionName === "topCoachCms") {
          const select =
            "_id firstName lastName profileImg status authStatus sportsCategories experience language";
          dataFromReferencedCollection = await ReferencedModel.find(
            {}
          ).populate({ path: "coach", select });
        } else if (referencedCollectionName === "topCategoryCms") {
          dataFromReferencedCollection = await ReferencedModel.find(
            {}
          ).populate({ path: "category" });
        } else {
          dataFromReferencedCollection = await ReferencedModel.find();
        }
        return {
          blockData: block,
          referencedData: dataFromReferencedCollection,
        };
      })
    );
    return res.status(200).json(blocksWithReferencedData);
  } catch (e) {
    console.error(e);
    return res.status(500).json({ error: "Internal Server Error" });
  }
};

export const getBlocksDataAdmin = async (req, res) => {
  try {
    const data = await BlockCms.find();
    res.status(200).json(data);
  } catch (e) {
    console.log(e);
    res.status(500).json("Internal Server Error");
  }
};

export const createBlock = async (req, res) => {
  try {
    if (!req.auth) {
      return res.status(400).json({ error: "Access not allowed" });
    }
    const addObj = {
      ...req.body,
    };

    if (req.body.max > 15) {
      return res
        .status(400)
        .json({ message: "Max value cannot be greater than 15" });
    }

    const data = await BlockCms.create(addObj);
    res.status(200).json(data);
  } catch (e) {
    console.log(e);
    res.status(500).json({ error: "Internal Server Error" });
  }
};

export const updateBlock = async (req, res) => {
  try {
    if (!req.auth) {
      return res.status(400).json({ error: "Access not allowed" });
    }
    const id = req.params.id;
    if (!id) {
      return res.status(400).json({ message: "Id not found" });
    }
    if (req.body.max > 15) {
      return res
        .status(400)
        .json({ message: "Max value cannot be greater than 15" });
    }
    let updateObj = {
      ...req.body,
    };
    const data = await BlockCms.findByIdAndUpdate(id, updateObj, {
      new: true,
    });
    res.status(200).json(data);
  } catch (e) {
    console.log(e);
    res.status(500).json({ error: "Internal Server Error" });
  }
};

export const updatePosition = async (req, res) => {
  try {
    if (!req.auth) {
      return res.status(400).json({ error: "Access not allowed" });
    }
    const { updates } = req.body;
    const updatePromises = updates.map(({ id, newPosition }) => {
      return BlockCms.findByIdAndUpdate(
        id,
        { position: newPosition },
        { new: true }
      );
    });

    Promise.all(updatePromises)
      .then((results) => {
        res.status(200).json({ result: "Position updated", status: "SUCCESS" });
      })
      .catch((error) => {
        res
          .status(400)
          .json({ error: "Error updating documents", message: error });
      });
  } catch (e) {
    console.log(e);
    res.status(500).send("Internal Server Error");
  }
};
