import mongoose from "mongoose";


const UserGroupSchema = new mongoose.Schema({
  name: {
    type: String,
    required: [true, "Name required"],
    unique: [true, "Name should be unique"],
    validate: {
      validator: function (name) {
        return name.trim().length > 2;
      },
      message: "Name should be of at least 3 characters",
    },
  },
  description: {
    type: String,
  },
  access_scopes: {
    academy: {
      type: [String],
      enum: ["read", "write", "delete"],
      default: [],
    },
    coach: {
      type: [String],
      enum: ["read", "write", "delete"],
      default: [],
    },
    course: {
      type: [String],
      enum: ["read", "write", "delete"],
      default: [],
    },
    category: {
      type: [String],
      enum: ["read", "write", "delete"],
      default: [],
    },
    booking: {
      type: [String],
      enum: ["read", "write", "delete"],
      default: [],
    },
    cms: {
      type: [String],
      enum: ["read", "write", "delete"],
      default: [],
    },
    player: {
      type: [String],
      enum: ["read", "write", "delete"],
      default: [],
    },
    user: {
      type: [String],
      enum: ["read", "write", "delete"],
      default: [],
    },
    contact : {
      type: [String],
      enum: ["read", "write", "delete"],
      default: [],
    },
    news : {
      type: [String],
      enum: ["read", "write", "delete"],
      default: [],
    },
    notify : {
      type: [String],
      enum: ["read", "write", "delete"],
      default: [],
    },
    reports : {
      type: [String],
      enum: ["read", "write", "delete"],
      default: [],
    },
    user_group: {
      type: [String],
      enum: ["read", "write", "delete"],
      default: [],
    }
  },
});

const UserGroup = mongoose.model("UserGroup", UserGroupSchema);

export default UserGroup;