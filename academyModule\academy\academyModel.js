import mongoose from "mongoose";
const { Schema, model } = mongoose;

const academySchema = new Schema(
  {
    name: {
      required: true,
      type: String,
    },
    mobile: {
      required: true,
      type: String,
      unique: true,
    },
    email: {
      type: String,
      unique: true,
      required: true,
    },
    gstNumber: {
      type: String,
    },
    academyImages: {
      type: [String],
      default: [],
    },
    sportsCategories: [
      {
        type: String,
      },
    ],
    officeAddress: {
      addressLine1: {
        type: String,
      },
      addressLine2: {
        type: String,
      },
      city: {
        type: String,
      },
      state: {
        type: String,
      },
      pinCode: {
        type: String,
      },
      country: {
        type: String,
      },
    },
    companyRegistrationNumber: {
      type: String,
    },
    linkedFacilities: [
      {
        facilityId: {
          type: String,
          required: true,
        },
        name: {
          type: String,
        },
        addressLine1: {
          type: String,
        },
        addressLine2: {
          type: String,
        },
        city: {
          type: String,
        },
        state: {
          type: String,
        },
        pinCode: {
          type: String,
        },
        country: {
          type: String,
        },
        amenities: {
          type: String,
        },
        location: {
          type: {
            type: String,
            default: "Point",
          },
          coordinates: [],
          is_location_exact: {
            type: Boolean,
            default: true,
          },
        },
      },
    ],
    status: {
      type: String,
      default: "inactive",
      enum: ["active", "inactive", "deactivated"],
    },
    authStatus: {
      type: String,
      required: true,
      default: "unauthorized",
      enum: ["authorized", "unauthorized"],
    },
    registrationDate: {
      type: Date,
    },
    approvalDate: {
      type: Date,
    },
    bankDetails: {
      accountNumber: {
        type: String,
      },
      accountHolderName: {
        type: String,
      },
      ifsc: {
        type: String,
      },
    },
    panNumber: {
      type: String,
      required: true,
    },
    panImage: {
      type: [String],
      default: [],
    },
    aadhaarNumber: {
      type: String,
      // required: true,
    },
    aadhaarImage: {
      type: [String],
      default: [],
    },
    platformFee: {
      type: Number,
    },
    profileImage: {
      type: String,
    },
  },
  { timestamps: true }
);

academySchema.pre("validate", function (next) {
  // Rule 1: If Aadhaar is provided, both number and image must be present
  const hasAadhaarNumber = !!this.aadhaarNumber;
  const hasAadhaarImage = !!this.aadhaarImage && this.aadhaarImage.length > 0;
  const hasCompanyReg = !!this.companyRegistrationNumber;

  if (
    (hasAadhaarNumber && !hasAadhaarImage) ||
    (!hasAadhaarNumber && hasAadhaarImage)
  ) {
    return next(
      new Error(
        "Both Aadhaar Number and Aadhaar Image must be provided together."
      )
    );
  }

  // Rule 2: At least one of Aadhaar (with image) or Company Registration Number must be present
  const hasAadhaar = hasAadhaarNumber && hasAadhaarImage;
  if (!hasAadhaar && !hasCompanyReg) {
    return next(
      new Error(
        "Either Aadhaar details (number and image) or Company Registration Number must be provided."
      )
    );
  }

  next();
});

academySchema.index({ email: 1, mobile: 1 }, { unique: true });

export const Academy = model("academy", academySchema);
