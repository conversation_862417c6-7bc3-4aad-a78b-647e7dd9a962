import mongoose from "mongoose";
const { Schema, model } = mongoose;
const TestimonialSchema = new Schema(
    {
        name: {
            type: String,
        },
        image: {
            type: String,
        },
        description: {
            type: String,
        },
        gender: {
            type: String
        },
        position: {
            type: Number
        }
    },
    { timestamps: true }
)

// Pre-save middleware to automatically assign position
TestimonialSchema.pre('save', async function (next) {
    try {
        if (!this.isNew || typeof this.position !== 'undefined') {
            return next(); // If not new or position already defined, skip
        }
        
        const maxPosition = await this.constructor.findOne({}, {}, { sort: { position: -1 } }); // Find the document with maximum position
        this.position = maxPosition ? maxPosition.position + 1 : 1; // Assign the next position
        next();
    } catch (error) {
        next(error);
    }
});

export const Testimonial = model("testimonial", TestimonialSchema);