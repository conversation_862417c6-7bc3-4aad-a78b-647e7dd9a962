import mongoose from "mongoose";
const { Schema, model } = mongoose;

const walletSchema = new Schema(
  {
    playerId: {
      type: Schema.Types.ObjectId,
      unique: true,
      required: true,
      ref:"player"
    },
    email: {
      type: String,
      unique: true,
      required: true,
    },
    balance: {
      type: Number,
      default: 0,
    },
    status: {
      type: String,
      default: "active",
      enum: ["active", "inActive"],
    },
    previous: {
      type: Number,
      required:true,
      default:0
    },
    
  },
  { timestamps: true }
);

export const Wallet = model("wallet", walletSchema);
