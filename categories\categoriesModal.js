import mongoose from "mongoose";
const { Schema, model } = mongoose;

const categoriesSchema = new Schema(
  {
    name: {
      type: String,
      required: true,
      unique: true,
    },
    lowerCaseName: {
      type: String,
      required: true,
    },
    description: {
      type: String
    },
    handle: {
      type: String, 
      required: true,
    },
    image:{
        type:String,
        required:true
    }
  },
  { timestamps: true }
);

export const Categories = model("categories", categoriesSchema);
