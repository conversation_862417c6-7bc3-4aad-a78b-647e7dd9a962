import AcademyUser from "../academyModule/academyUser/academyUserModal.js";
import { Coach } from "../coaches/coachModal.js";
import { Player } from "../player/playerModal.js";
import User from "../user-management/User/userModal.js";
import { verifyToken } from "./jwt.js";
import mongoose from "mongoose";

export const coachProtect = async (req, res, next) => {
  try {
    if (!req.headers.authorization) {
      return res.status(401).json({ error: "User not authorized" });
    }
    let token = req.headers.authorization.split("Bearer ")[1];
    if (!token) {
      return res.status(401).json({ error: "Token not found" });
    }
    const payload = await verifyToken(token);
    if (payload?.userType !== "coach") {
      next();
    } else {
      const user = await Coach.findById(payload.id).lean().exec();
      req.user = user;
      req.auth = true;
      req.userType = "coach";
      next();
    }
  } catch (e) {
    return res.status(401).end();
  }
};

export const playerProtect = async (req, res, next) => {
  try {
    if (!req.headers.authorization) {
      return res.status(401).json({ message: "User not authorized" });
    }
    let token = req.headers.authorization.split("Bearer ")[1];
    if (!token) {
      return res.status(401).json({ message: "Token not found" });
    }
    const payload = await verifyToken(token);
    if (payload?.userType !== "player") {
      next();
    } else {
      const user = await Player.findById(payload.id).lean().exec();
      req.user = user;
      req.auth = true;
      next();
    }
  } catch (e) {
    console.log(e);
    return res.status(500).end();
  }
};

export const academyUserProtect = (isPublic = false) => {
  return async (req, res, next) => {
    try {
      let token;

      // Try from Authorization header
      if (
        req?.headers?.authorization &&
        req?.headers?.authorization?.startsWith("Bearer ")
      ) {
        token = req?.headers?.authorization?.split(" ")[1];
      }

      // Fallback to cookie
      if (!token && req?.cookies?.accessToken) {
        token = req?.cookies?.accessToken;
      }

      if (!token) {
        if (isPublic) {
          req.auth = false;
          req.userType = "public";
          return next();
        } else {
          return res.status(401).json({ message: "Token not found" });
        }
      }

      const payload = await verifyToken(token);

      if (payload?.userType !== "academy") {
        return next();
      } else {
        req.user = payload;
        req.auth = true;
        req.accessScopes = payload?.accessScopes || {};
        return next();
      }
    } catch (err) {
      console.error("Auth error:", err);
      if (isPublic) {
        req.auth = false;
        req.userType = "public";
        return next();
      } else {
        return res.status(401).json({ message: "Invalid or expired token" });
      }
    }
  };
};

export const flexibleAuth = (module, scope, isPublic = false) => {
  return async (req, res, next) => {
    const auth =
      req.headers.authorization &&
      req.headers.authorization.startsWith("Bearer ")
        ? req.headers.authorization.split(" ")[1]
        : req.cookies.accessToken;

    if (!auth) {
      if (isPublic) {
        req.auth = false;
        req.userType = "public";
        return next();
      } else {
        return res.status(401).json({ message: "Token not found" });
      }
    }

    try {
      const token = auth;
      const decodedUser = await verifyToken(token);

      if (decodedUser.userType === "Admin") {
        // Check admin permissions
        const hasPermission = await checkAdminPermission(
          decodedUser.userId,
          module,
          scope
        );
        if (hasPermission) {
          req.body.current_user = decodedUser;
          req.user = decodedUser;
          req.auth = true;
          req.userType = decodedUser.userType;
          return next();
        } else {
          return res.status(403).json({ err: "Forbidden" });
        }
      } else if (decodedUser.userType === "academy") {
        // Academy user or other authenticated user
        const hasPermission = await checkAcademyPermission(
          decodedUser.id,
          module,
          scope
        );
        if (hasPermission) {
          req.user = decodedUser;
          req.auth = true;
          req.userType = decodedUser.userType;
          return next();
        } else {
          return res.status(403).json({ err: "Forbidden" });
        }
      } else {
        return next();
      }
    } catch (err) {
      // Invalid token - treat as public registration
      req.auth = false;
      req.userType = "public";
      return next();
    }
  };
};

const checkAdminPermission = async (userId, module, scope) => {
  let user = await User.aggregate([
    {
      $match: {
        _id: new mongoose.Types.ObjectId(userId),
      },
    },
    {
      $addFields: {
        usergroupids: {
          $map: {
            input: "$userGroup",
            as: "group",
            in: {
              $toObjectId: "$$group",
            },
          },
        },
      },
    },
    {
      $lookup: {
        from: "usergroups",
        localField: "usergroupids",
        foreignField: "_id",
        as: "userGroups",
      },
    },
    {
      $project: {
        userGroups: 1,
      },
    },
  ]);

  if (!user.length) {
    return false;
  }
  let scopes = user[0].userGroups.flatMap((x) => {
    return x.access_scopes[module];
  });
  if (scopes.includes(scope)) {
    return true;
  } else {
    return false;
  }
};

const checkAcademyPermission = async (userId, module, scope) => {
  let user = await AcademyUser.aggregate([
    {
      $match: {
        _id: new mongoose.Types.ObjectId(userId),
      },
    },
    {
      $addFields: {
        usergroupids: {
          $map: {
            input: "$academyUserGroups",
            as: "group",
            in: {
              $toObjectId: "$$group",
            },
          },
        },
      },
    },
    {
      $lookup: {
        from: "academyusergroups",
        localField: "academyUserGroups",
        foreignField: "_id",
        as: "academyUserGroups",
      },
    },
    {
      $project: {
        academyUserGroups: 1,
      },
    },
  ]);

  if (!user.length) {
    return false;
  }
  let scopes = user[0].academyUserGroups.flatMap((x) => {
    return x.access_scopes[module];
  });
  if (scopes.includes(scope)) {
    return true;
  } else {
    return false;
  }
};
