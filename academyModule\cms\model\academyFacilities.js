import mongoose from "mongoose";
const { Schema, model } = mongoose;

const academyFacilitiesSchema = new Schema(
  {
    academy: { type: Schema.Types.ObjectId, ref: "academy" },
    facilityId: { type: String },
    position: { type: Number },
    isActive: { type: Boolean, default: true },
  },
  { timestamps: true }
);

// Pre-save middleware to automatically assign position
academyFacilitiesSchema.pre("save", async function (next) {
  try {
    if (!this.isNew || typeof this.position !== "undefined") {
      return next();
    }

    const maxPosition = await this.constructor.findOne(
      { academy: this.academy },
      {},
      { sort: { position: -1 } }
    );
    this.position = maxPosition ? maxPosition.position + 1 : 1;
    next();
  } catch (error) {
    next(error);
  }
});

export const AcademyFacilities = model(
  "academyFacilities",
  academyFacilitiesSchema
);
