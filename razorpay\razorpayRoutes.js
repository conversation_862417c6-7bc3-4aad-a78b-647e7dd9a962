import express from "express";
import {
  checkout,
  createPayment,
  getPaymentDetails,
  paymentVerification,
} from "./razorpayController.js";
import { playerProtect } from "../utils/auth.js";

const router = express.Router();

router.route("/checkout").post(playerProtect, checkout);
router.route("/paymentVerification").post(playerProtect, paymentVerification);
router.route("/payment").post(playerProtect, createPayment);
router.route("/:id").get(playerProtect, getPaymentDetails);
export default router;
