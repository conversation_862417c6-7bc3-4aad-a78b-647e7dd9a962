import ms from "ms";
import bcrypt from "bcryptjs";
import AppError from "../../middlewares/appError.js";
import crypto from "crypto";

export const generateSessionId = () => {
  return crypto.randomBytes(16).toString("hex");
};

export const calculateRefreshExpiresAt = (expiresIn) => {
  const milliseconds = ms(expiresIn);
  if (!milliseconds) {
    throw new AppError("Invalid expiration format", 400, {
      errors: [
        {
          field: "expiresIn",
          message: "Invalid expiration format",
        },
      ],
    });
  }
  return new Date(Date.now() + milliseconds);
};

export const comparePassword = async (password, hashedPassword) => {
  return await bcrypt.compare(password, hashedPassword);
};
