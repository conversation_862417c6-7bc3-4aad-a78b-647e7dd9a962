import mongoose from "mongoose";
const { Schema, model } = mongoose;

const courseSchema = new Schema(
  {
    coach_id: {
      type: Schema.Types.ObjectId,
      required: true,
      ref: "coach",
    },
    academy_id: {
      type: Schema.Types.ObjectId,
      ref: "academy",
    },
    coachName: {
      type: String,
      required: true,
    },
    coachEmail: {
      type: String,
      required: true,
    },
    courseName: {
      type: String,
      required: true,
    },
    description: {
      type: String,
      required: true,
    },
    images: [
      {
        url: {
          type: String,
          required: true,
        },
      },
    ],
    facility: {
      name: {
        type: String,
        required: true,
      },
      addressLine1: {
        type: String,
      },
      addressLine2: {
        type: String,
      },
      city: {
        type: String,
      },
      state: {
        type: String,
      },
      pinCode: {
        type: String,
      },
      country: {
        type: String,
      },
      location: {
        type: {
          type: String,
          default: "Point",
        },
        coordinates: [],
        is_location_exact: {
          type: Boolean,
          default: true,
        },
      },
    },
    category: {
      type: String,
      required: true,
    },
    sessionType: {
      type: String,
      required: true,
      enum: ["group", "individual"],
    },
    classType: {
      type: String,
      required: true,
      enum: ["class", "course"],
    },
    camp: {
      type: Boolean,
      default: false,
    },
    campName: {
      type: String,
    },
    fees: {
      feesCourse: {
        type: Number,
      },
      fees30: {
        type: Number,
      },
      fees45: {
        type: Number,
      },
      fees60: {
        type: Number,
      },
      fees: {
        type: Number,
      },
    },
    maxGroupSize: {
      type: Number,
      required: true,
    },
    playerEnrolled: {
      type: Number,
      default: 0,
    },
    proficiency: [
      {
        type: String,
        enum: ["advance", "intermediate", "beginner"],
      },
    ],
    status: {
      type: String,
      default: "active",
      enum: ["active", "inactive"],
    },
    dates: {
      startDate: {
        type: Date,
        required: true,
      },
      endDate: {
        type: Date,
        required: true,
      },
      startTime: {
        type: String,
        required: true,
      },
      endTime: {
        type: String,
        required: true,
      },
      days: [{ type: String, required: true }],
      dates: [
        {
          type: Date,
        },
      ],
    },
    ratings: {
      stars: {
        type: Number,
        default: 0,
      },
      reviews: {
        type: String,
      },
      likes: {
        type: Number,
        default: 0,
      },
      noOfRatings: {
        type: Number,
        default: 0,
      },
    },
    amenitiesProvided: {
      type: String,
    },
    whatYouHaveToBring: {
      type: String,
    },
    cancellationPolicy: {
      type: String,
    },
    eventId: {
      type: String,
    },
    coachStatus: {
      type: String,
      default: "active",
      enum: ["active", "inactive"],
    },
    customImage: {
      type: Boolean,
      default: false,
    },
  },
  { timestamps: true }
);

export const Course = model("course", courseSchema);
