import { Router } from "express";
import {
  getAllTransactions,
  getTransactionById,
} from "./transactionController.js";
import { playerProtect } from "../../utils/auth.js";
import authenticateUserAccess from "../../Helpers/authenticationUser.js";
const router = Router();

router
  .route("/")
  .get(
    authenticateUserAccess("transaction", "read"),
    playerProtect,
    getAllTransactions
  );
router
  .route("/:id")
  .get(
    authenticateUserAccess("transaction", "read"),
    playerProtect,
    getTransactionById
  );

export default router;
