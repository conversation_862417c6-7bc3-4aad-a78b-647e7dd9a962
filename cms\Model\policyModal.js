import mongoose from "mongoose";
const { Schema, model, SchemaType } = mongoose;
const PolicySchema = new Schema(
    {
        TermsAndConditions: {
            type: String,
        },
        privacyPolicy: {
            type: String,
        },
        customerGrievancePolicy: {
            type: String,
        }
    },
    { timestamps: true }
)

export const Policy = model("Policy", PolicySchema);