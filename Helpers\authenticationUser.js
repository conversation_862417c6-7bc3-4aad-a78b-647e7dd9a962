import mongoose from "mongoose";
import jwt from "jsonwebtoken";
import User from "../user-management/User/userModal.js";
import { SECRETS } from "../utils/config.js";


let scopes = {
  dashboard: ["read", "write", "delete"],
  coach: ["read", "write", "delete"],
  course: ["read", "write", "delete"],
  category: ["read", "write", "delete"],
  booking: ["read", "write", "delete"],
  cms: ["read", "write", "delete"],
  player: ["read", "write", "delete"],
  user: ["read", "write", "delete"],
  user_group: ["read", "write", "delete"],
};

const authenticateUserAccess = (module, scope) => {
  return async (req, res, next) => {
    const auth = req.headers.authorization;

    if (!auth) {
      next();
      return;
    }

    const token = auth.split(" ")[1];

    try {
      const decodedUser = jwt.verify(token, SECRETS.jwt);
      if (decodedUser.userType !== "Admin") {
        next();
      } else {
        let userId = decodedUser.userId;
        let user = await User.aggregate([
          {
            $match: {
              _id: new mongoose.Types.ObjectId(userId),
            },
          },
          {
            $addFields: {
              usergroupids: {
                $map: {
                  input: "$userGroup",
                  as: "group",
                  in: {
                    $toObjectId: "$$group",
                  },
                },
              },
            },
          },
          {
            $lookup: {
              from: "usergroups",
              localField: "usergroupids",
              foreignField: "_id",
              as: "userGroups",
            },
          },
          {
            $project: {
              userGroups: 1,
            },
          },
        ]);

        if (!user.length) {
          return res.status(401).json({ err: "Invalid token" });
        }
        let scopes = user[0].userGroups.flatMap((x) => {
          return x.access_scopes[module];
        });
        if (scopes.includes(scope)) {
          req.body.current_user = decodedUser;
          req.auth = true;
          req.userType=decodedUser.userType;
          next();
        } else {
          return res.status(403).json({ err: "Forbidden" });
        }
      }
    } catch (err) {
      return res.status(401).json({ err: "Invalid token" });
    }
  };
};

export default authenticateUserAccess;
