import { Course } from "./courseModal.js";
import { Coach } from "../coaches/coachModal.js";
import { Academy } from "../academyModule/academy/academyModel.js";

export const getSearchResults = async (req, res) => {
  let {
    page,
    min,
    max,
    q: search,
    fees,
    createdAt,
    ratings,
    lat,
    long,
  } = req.query;
  try {
    const expectedFilters = [
      "category",
      "classType",
      "sessionType",
      "proficiency",
      "status",
      "coachStatus",
      "coachEmail",
    ];
    const searchParameters = ["courseName", "coachName", "category"];
    page = page || 1;
    let limitVal = 10;
    let skipValue = (page - 1) * limitVal;

    const compound = {};
    let shouldArray = null;
    if (search) {
      shouldArray = searchParameters.map((x) => ({
        autocomplete: {
          query: search,
          path: x,
          fuzzy: {
            maxEdits: 2,
            prefixLength: 2,
            maxExpansions: 256,
          },
        },
      }));
      compound.should = shouldArray;
      compound.minimumShouldMatch = 1;
    }

    const filterArray = [
      {
        queryString: {
          defaultPath: "status",
          query: "active",
        },
      },
      {
        queryString: {
          defaultPath: "coachStatus",
          query: "active",
        },
      },
    ];

    for (let filterName in req.query) {
      if (expectedFilters.includes(filterName)) {
        let filterValue = req.query[filterName];
        if (typeof filterValue !== "string") {
          filterValue = filterValue.join(" or ");
        }
        filterArray.push({
          queryString: {
            defaultPath: filterName,
            query: filterValue,
          },
        });
      }
    }

    if (min || max) {
      filterArray.push({
        range: {
          path: "fees.fees",
          gt: min ? parseFloat(min) : undefined,
          lt: max ? parseFloat(max) : undefined,
        },
      });
    }

    // ! -> original lat long condition
    if (lat && long) {
      filterArray.push({
        geoWithin: {
          circle: {
            center: {
              type: "Point",
              coordinates: [parseFloat(lat), parseFloat(long)],
            },
            radius: 100000,
          },
          path: "facility.location",
        },
      });
    }
    if (filterArray.length) {
      compound.filter = filterArray;
    }

    const $search = {
      index: "course_ac",
      compound: compound,
    };

    const sort = {};
    if (fees) {
      sort["fees.fees"] = fees === "asc" ? 1 : -1;
    }
    if (createdAt) {
      sort.createdAt = createdAt === "asc" ? 1 : -1;
    }
    if (ratings) {
      sort["ratings.stars"] = ratings === "asc" ? 1 : -1;
    }

    if (Object.keys(sort).length > 0) {
      $search.sort = sort;
    }
    const data = await Course.aggregate([
      { $search: $search },
      {
        $project: {
          coach_id: 1,
          academy_id: 1,
          coachName: 1,
          coachEmail: 1,
          courseName: 1,
          description: 1,
          images: 1,
          facility: 1,
          category: 1,
          sessionType: 1,
          classType: 1,
          camp: 1,
          campName: 1,
          fees: 1,
          maxGroupSize: 1,
          playerEnrolled: 1,
          proficiency: 1,
          status: 1,
          coachStatus: 1,
          dates: 1,
          ratings: 1,
          amenitiesProvided: 1,
          whatYouHaveToBring: 1,
          cancellationPolicy: 1,
        },
      },
      { $skip: skipValue },
      { $limit: limitVal },
    ]);
    let coachData = [];
    let academyData = [];
    if (search && search.trim() !== "") {
      coachData = await Coach.aggregate([
        {
          $search: {
            index: "coach_ac",
            compound: {
              should: [
                {
                  autocomplete: {
                    query: search,
                    path: "firstName",
                    fuzzy: { maxEdits: 2 },
                  },
                },
                {
                  autocomplete: {
                    query: search,
                    path: "lastName",
                    fuzzy: { maxEdits: 2 },
                  },
                },
                {
                  autocomplete: {
                    query: search,
                    path: "sportsCategories",
                    fuzzy: { maxEdits: 2 },
                    score: { boost: { value: 2 } },
                  },
                },
              ],
              minimumShouldMatch: 1,
            },
          },
        },
        {
          $project: {
            coach_id: "$_id",
            coachName: "$firstName",
            lastName: 1,
            email: 1,
            courseName: "No course",
            image: "$profileImg",
            images: "$profileImg",
            status: "active",
            coachStatus: "active",
            course: "false",
            sportsCategories: "$sportsCategories",
          },
        },
        { $skip: skipValue },
        { $limit: limitVal },
      ]);
      academyData = await Academy.aggregate([
        {
          $search: {
            index: "academy_ac",
            compound: {
              should: [
                {
                  autocomplete: {
                    query: search,
                    path: "name",
                    fuzzy: { maxEdits: 2 },
                  },
                },
                {
                  autocomplete: {
                    query: search,
                    path: "sportsCategories",
                    fuzzy: { maxEdits: 2 },
                    score: { boost: { value: 2 } },
                  },
                },
              ],
              minimumShouldMatch: 1,
            },
          },
        },
        {
          $project: {
            academy_id: "$_id",
            academyName: "$name",
            academyEmail: "$email",
            profileImage: "$profileImage",
            academyImages: "$academyImages",
            academyStatus: "active",
            academyType: "academy",
            academyAddress: "$officeAddress",
            academyLocation: "$location",
            academySportsCategories: "$sportsCategories",
          },
        },
        { $skip: skipValue },
        { $limit: limitVal },
      ]);
    }

    const filteredCoachData = coachData.filter(
      (coach) =>
        !data.some(
          (course) => course.coach_id.toString() === coach.coach_id.toString()
        )
    );
    const filteredAcademyData = academyData.filter(
      (academy) =>
        !data.some(
          (course) =>
            course?.academy_id?.toString() === academy?.academy_id?.toString()
        )
    );
    const finalResults = {
      coursesAndCoaches: [...data, ...filteredCoachData],
      academyData: [...filteredAcademyData],
    };
    return res.status(200).json(finalResults);
  } catch (err) {
    console.error("Error in getSearchResults:", err);
    return res
      .status(500)
      .json({ error: err.message || "Internal server error" });
  }
};
