import { AcademyFacilities } from "./model/academyFacilities.js";
import AppError from "../../middlewares/appError.js";
import AppSuccess from "../../middlewares/appSuccess.js";
import { catchAsync } from "../../utils/helpers.js";
import mongoose from "mongoose";
import { AcademyTopCoach } from "./model/academyTopCoachModal.js";
import { Coach } from "../../coaches/coachModal.js";
import { AcademyTopCourse } from "./model/academyTopCourseModal.js";
import { Course } from "../../courses/courseModal.js";
import { AcademyDescription } from "./model/academyDescription.js";
import { AcademyTestimonial } from "./model/academyTestimonials.js";
import { AcademyBlockCms } from "./model/academyBlocksModal.js";

// Fetch all facilities for an academy
export const getAcademyFacilities = catchAsync(async (req, res, next) => {
  const academyId = req.user.academyId._id;
  const facilities = await AcademyFacilities.find({ academy: academyId })
    .sort({ position: 1 })
    .populate({
      path: "academy",
      select: "name linkedFacilities",
    });

  console.log("facilities", JSON.stringify(facilities, null, 2));

  const formatted = facilities.map((facilityDoc) => {
    // Match by stable facilityId
    const matchedFacility = facilityDoc.academy.linkedFacilities.find(
      (f) => f.facilityId === facilityDoc.facilityId
    );

    return {
      _id: facilityDoc._id,
      facilityId: facilityDoc.facilityId,
      position: facilityDoc.position,
      isActive: facilityDoc.isActive,
      createdAt: facilityDoc.createdAt,
      updatedAt: facilityDoc.updatedAt,
      academy: {
        _id: facilityDoc.academy._id,
        name: facilityDoc.academy.name,
        facilityDetails: matchedFacility || null,
      },
    };
  });

  return new AppSuccess(res, {
    message: "Facilities fetched successfully",
    data: formatted,
    statusCode: 200,
  });
});

// Toggle facility status (active/inactive)
export const toggleAcademyFacilityStatus = catchAsync(
  async (req, res, next) => {
    const academyId = req.user.academyId._id;
    const { facilityId } = req.params; // This is now the stable facilityId

    if (!academyId || !facilityId) {
      throw new AppError("Academy ID and Facility ID are required", 400, {
        errors: [
          { field: "academyId", message: "Academy ID is required." },
          { field: "facilityId", message: "Facility ID is required." },
        ],
      });
    }

    const facility = await AcademyFacilities.findOne({
      academy: academyId,
      facilityId: facilityId, // Use stable facilityId
    });

    if (!facility) {
      throw new AppError("Facility not found for this academy", 404, {
        errors: [
          {
            field: "facilityId",
            message: "Facility not found for this academy.",
          },
        ],
      });
    }

    facility.isActive = !facility.isActive;
    await facility.save();

    return new AppSuccess(res, {
      message: `Facility status toggled to ${
        facility.isActive ? "active" : "inactive"
      }`,
      data: facility,
      statusCode: 200,
    });
  }
);

// Fetch only active facilities for an academy
export const getActiveAcademyFacilities = catchAsync(async (req, res, next) => {
  const academyId = req.params.academyId;
  const tokenAcademyId = req.user?.academyId?._id;

  if (!academyId) {
    throw new AppError("Academy ID not found in user context", 400, {
      errors: [{ field: "academyId", message: "Academy ID is required." }],
    });
  }

  if (tokenAcademyId && academyId.toString() !== tokenAcademyId.toString()) {
    throw new AppError("Access not allowed", 403, {
      errors: [
        { field: "auth", message: "You are not authorized for this academy" },
      ],
    });
  }

  const facilities = await AcademyFacilities.find({
    academy: academyId,
    isActive: true,
  })
    .sort({ position: 1 })
    .populate({
      path: "academy",
      select: "name linkedFacilities",
    });

  const formatted = facilities.map((facilityDoc) => {
    // Match by stable facilityId
    const matchedFacility = facilityDoc.academy.linkedFacilities.find(
      (f) => f.facilityId === facilityDoc.facilityId
    );

    return {
      _id: facilityDoc._id,
      facilityId: facilityDoc.facilityId,
      position: facilityDoc.position,
      isActive: facilityDoc.isActive,
      createdAt: facilityDoc.createdAt,
      updatedAt: facilityDoc.updatedAt,
      academy: {
        _id: facilityDoc.academy._id,
        name: facilityDoc.academy.name,
      },
      facilityDetails: matchedFacility || null,
    };
  });

  return new AppSuccess(res, {
    message: "Active facilities fetched successfully",
    data: formatted,
    statusCode: 200,
  });
});

// Update position of academy facilities
export const updateAcademyFacilityPosition = catchAsync(
  async (req, res, next) => {
    const { updates } = req.body;
    if (!Array.isArray(updates)) {
      return next(
        new AppError("Not an array", 400, {
          errors: [{ field: "updates", message: "Not an array" }],
        })
      );
    }

    const updatePromises = updates.map(async ({ id, newPosition }) => {
      const updatedFacility = await AcademyFacilities.findOneAndUpdate(
        { facilityId: id }, // Use stable facilityId
        { position: newPosition },
        { new: true }
      );
      return updatedFacility;
    });

    const results = await Promise.all(updatePromises);
    return new AppSuccess(res, {
      message: "Position updated successfully",
      data: results,
      statusCode: 200,
    });
  }
);

// Get all top coaches for an academy (global format)
export const getTopCoaches = catchAsync(async (req, res, next) => {
  const academyId = req.params.academyId;
  const tokenAcademyId = req.user?.academyId?._id;

  if (!academyId) {
    throw new AppError("academyId is required", 400, {
      errors: [{ field: "academyId", message: "academyId is required" }],
    });
  }
  // Only compare if tokenAcademyId exists
  if (tokenAcademyId && academyId.toString() !== tokenAcademyId.toString()) {
    throw new AppError("Access not allowed", 403, {
      errors: [
        { field: "auth", message: "You are not authorized for this academy" },
      ],
    });
  }

  const topCoaches = await AcademyTopCoach.find({ academy: academyId })
    .populate({
      path: "coach",
      select: "firstName lastName email mobile status authStatus profileImg",
    })
    .sort({ position: 1 });
  return new AppSuccess(res, {
    message: "Top coaches fetched successfully",
    data: topCoaches,
    statusCode: 200,
  });
});

export const addTopCoach = catchAsync(async (req, res, next) => {
  if (!req.auth) {
    throw new AppError("Access not allowed", 400, {
      errors: [{ field: "auth", message: "Access not allowed" }],
    });
  }

  const academyId = req.user?.academyId?._id;
  if (!academyId) {
    throw new AppError("Academy ID not found in token", 400, {
      errors: [{ field: "academyId", message: "Academy ID is required" }],
    });
  }

  const { documents } = req.body;

  if (!Array.isArray(documents)) {
    throw new AppError("Not an array", 400, {
      errors: [{ field: "documents", message: "Not an array" }],
    });
  }
  // Validate that all coach IDs are active and authorized
  const coachIds = documents.map((doc) => doc.coach);
  const validCoaches = await Coach.find({
    _id: { $in: coachIds },
    status: "active",
    authStatus: "authorized",
    academyId: academyId,
  }).select("_id");

  const validCoachIds = validCoaches.map((c) => c._id.toString());
  const invalidCoachIds = coachIds.filter(
    (id) => !validCoachIds.includes(id.toString())
  );

  if (invalidCoachIds.length > 0) {
    throw new AppError("Some coaches are not active or authorized", 400, {
      errors: invalidCoachIds.map((id) => ({
        field: "coach",
        message: `Coach with ID ${id} is not active or authorized`,
      })),
    });
  }

  const block = await AcademyBlockCms.findOne({
    academy: academyId,
    collectionName: "academyTopCoachCms",
  });
  if (documents.length > block.max) {
    throw new AppError(
      `Maximum limit reached, cannot be grater than ${block.max}`,
      400,
      {
        errors: [
          {
            field: "topCoaches",
            message: `Maximum limit reached, cannot be grater than ${block.max}`,
          },
        ],
      }
    );
  }

  // Proceed with replacement
  await AcademyTopCoach.deleteMany({ academy: academyId });
  const coaches = await AcademyTopCoach.insertMany(
    documents.map((doc) => ({
      ...doc,
      academy: academyId,
    }))
  );
  return new AppSuccess(res, {
    message: "Coach updated successfully",
    data: coaches,
    statusCode: 200,
  });
});

// Update top coach position
export const updateCoachPosition = catchAsync(async (req, res, next) => {
  if (!req.auth) {
    throw new AppError("Access not allowed", 400, {
      errors: [{ field: "auth", message: "Access not allowed" }],
    });
  }
  const academyId = req.user?.academyId?._id;
  if (!academyId) {
    throw new AppError("academyId not found in token", 400, {
      errors: [{ field: "academyId", message: "academyId is required" }],
    });
  }
  const { updates } = req.body;
  if (!Array.isArray(updates)) {
    throw new AppError("Not an array", 400, {
      errors: [{ field: "updates", message: "Not an array" }],
    });
  }

  const updatePromises = updates.map(({ id, newPosition }) => {
    return AcademyTopCoach.findOneAndUpdate(
      { _id: id, academy: academyId },
      { position: newPosition },
      { new: true }
    );
  });

  const results = await Promise.all(updatePromises);
  return new AppSuccess(res, {
    message: "Position updated successfully",
    data: results,
    statusCode: 200,
  });
});

// ==================== COURSE FUNCTIONS ====================

// Get all top courses for an academy (global format)
export const getTopCourses = catchAsync(async (req, res, next) => {
  const academyId = req.params.academyId;
  const tokenAcademyId = req.user?.academyId?._id;

  if (tokenAcademyId && academyId.toString() !== tokenAcademyId.toString()) {
    throw new AppError("Access not allowed", 403, {
      errors: [
        { field: "auth", message: "You are not authorized for this academy" },
      ],
    });
  }
  if (!academyId) {
    throw new AppError("academyId is required", 400, {
      errors: [{ field: "academyId", message: "academyId is required" }],
    });
  }
  const topCourses = await AcademyTopCourse.find({ academy: academyId })
    .populate({
      path: "course",
      select:
        "courseName description images facility coach_id coachName status authStatus",
    })
    .sort({ position: 1 });
  return new AppSuccess(res, {
    message: "Top courses fetched successfully",
    data: topCourses,
    statusCode: 200,
  });
});

export const addTopCourse = catchAsync(async (req, res, next) => {
  if (!req.auth) {
    throw new AppError("Access not allowed", 400, {
      errors: [{ field: "auth", message: "Access not allowed" }],
    });
  }

  const academyId = req.user?.academyId?._id;
  if (!academyId) {
    throw new AppError("Academy ID not found in token", 400, {
      errors: [{ field: "academyId", message: "Academy ID is required" }],
    });
  }

  const { documents } = req.body;

  if (!Array.isArray(documents)) {
    throw new AppError("Not an array", 400, {
      errors: [{ field: "documents", message: "Not an array" }],
    });
  }

  // Validate that all course IDs are active and belong to the academy
  const courseIds = documents.map((doc) => doc.course);

  // Find courses that are active and belong to the academy from token
  const validCourses = await Course.find({
    _id: { $in: courseIds },
    status: "active",
    academy_id: academyId, // Ensure courses belong to the academy from token
  }).select("_id academyId");

  const validCourseIds = validCourses.map((c) => c._id.toString());
  const invalidCourseIds = courseIds.filter(
    (id) => !validCourseIds.includes(id.toString())
  );
  if (invalidCourseIds.length > 0) {
    // Get more details about invalid courses for better error messages
    const invalidCourseDetails = await Course.find({
      _id: { $in: invalidCourseIds },
    }).select("_id academyId status");

    throw new AppError(
      "Some courses are not active or don't belong to your academy",
      400,
      {
        errors: invalidCourseIds.map((id) => {
          const courseDetail = invalidCourseDetails.find(
            (c) => c._id.toString() === id.toString()
          );
          let message = `Course with ID ${id} is not valid`;

          if (courseDetail) {
            if (courseDetail.status !== "active") {
              message = `Course with ID ${id} is not active (status: ${courseDetail.status})`;
            } else if (
              courseDetail.academy_id?.toString() !== academyId.toString()
            ) {
              message = `Course with ID ${id} doesn't belong to your academy`;
            }
          } else {
            message = `Course with ID ${id} not found`;
          }

          return {
            field: "course",
            message: message,
          };
        }),
      }
    );
  }

  const block = await AcademyBlockCms.findOne({
    academy: academyId,
    collectionName: "academyTopCourseCms",
  });
  if (documents.length > block.max) {
    throw new AppError(
      `Maximum limit reached, cannot be grater than ${block.max}`,
      400,
      {
        errors: [
          {
            field: "topCourses",
            message: `Maximum limit reached, cannot be grater than ${block.max}`,
          },
        ],
      }
    );
  }

  // Proceed with replacement
  await AcademyTopCourse.deleteMany({ academy: academyId });
  const courses = await AcademyTopCourse.insertMany(
    documents.map((doc) => ({
      ...doc,
      academy: academyId,
    }))
  );
  return new AppSuccess(res, {
    message: "Courses updated successfully",
    data: courses,
    statusCode: 200,
  });
});

// Update top course position
export const updateCoursePosition = catchAsync(async (req, res, next) => {
  if (!req.auth) {
    throw new AppError("Access not allowed", 400, {
      errors: [{ field: "auth", message: "Access not allowed" }],
    });
  }
  const academyId = req.user?.academyId?._id;
  if (!academyId) {
    throw new AppError("academyId not found in token", 400, {
      errors: [{ field: "academyId", message: "academyId is required" }],
    });
  }
  const { updates } = req.body;
  if (!Array.isArray(updates)) {
    throw new AppError("Not an array", 400, {
      errors: [{ field: "updates", message: "Not an array" }],
    });
  }

  const updatePromises = updates.map(({ id, newPosition }) => {
    return AcademyTopCourse.findOneAndUpdate(
      { _id: id, academy: academyId },
      { position: newPosition },
      { new: true }
    );
  });

  const results = await Promise.all(updatePromises);
  return new AppSuccess(res, {
    message: "Course position updated successfully",
    data: results,
    statusCode: 200,
  });
});

// ==================== ACADEMY DESCRIPTION FUNCTIONS ====================

// Get academy description by academy ID (from token)
export const getAcademyDescription = catchAsync(async (req, res, next) => {
  const academyId = req.params.academyId;
  const tokenAcademyId = req.user?.academyId?._id;

  if (!academyId) {
    throw new AppError("Academy ID not found in token", 400, {
      errors: [{ field: "academyId", message: "Academy ID is required" }],
    });
  }

  if (tokenAcademyId && academyId.toString() !== tokenAcademyId.toString()) {
    throw new AppError("Access not allowed", 403, {
      errors: [
        { field: "auth", message: "You are not authorized for this academy" },
      ],
    });
  }
  const description = await AcademyDescription.findOne({
    academy: academyId,
  }).populate({
    path: "academy",
    select: "name email status authStatus",
  });

  if (!description) {
    return new AppSuccess(res, {
      message: "No description found for this academy",
      data: null,
      statusCode: 200,
    });
  }

  return new AppSuccess(res, {
    message: "Academy description fetched successfully",
    data: description,
    statusCode: 200,
  });
});

// Post/Update academy description
export const postAcademyDescription = catchAsync(async (req, res, next) => {
  const academyId = req.user?.academyId?._id;
  let { description } = req.body;
  description = description.trim();
  if (description.length < 10) {
    throw new AppError("Description must be at least 10 characters long", 400, {
      errors: [
        {
          field: "description",
          message: "Description must be at least 10 characters long",
        },
      ],
    });
  }

  if (!academyId) {
    throw new AppError("Academy ID not found in token", 400, {
      errors: [{ field: "academyId", message: "Academy ID is required" }],
    });
  }
  // Check if description already exists for this academy
  const existingDescription = await AcademyDescription.findOne({
    academy: academyId,
  });

  let result;
  if (existingDescription) {
    // Update existing description
    result = await AcademyDescription.findOneAndUpdate(
      { academy: academyId },
      { description: description },
      { new: true, runValidators: true }
    ).populate({
      path: "academy",
      select: "name email status authStatus",
    });

    return new AppSuccess(res, {
      message: "Academy description updated successfully",
      data: result,
      statusCode: 200,
    });
  } else {
    // Create new description
    result = await AcademyDescription.create({
      academy: academyId,
      description: description.trim(),
    });

    // Populate the academy details
    await result.populate({
      path: "academy",
      select: "name email status authStatus",
    });

    return new AppSuccess(res, {
      message: "Academy description created successfully",
      data: result,
      statusCode: 201,
    });
  }
});

// Blocks

export const getAcademyBlocks = catchAsync(async (req, res, next) => {
  const academyId = req.params.academyId;
  const academyBlockCms = await AcademyBlockCms.find({
    academy: academyId,
    visibility: true,
  });
  const blocksWithReferencedData = await Promise.all(
    academyBlockCms.map(async (block) => {
      const referencedCollectionName = block.collectionName;
      if (!referencedCollectionName) {
        return {
          blockData: block,
          referencedData: null,
        };
      }
      if (!mongoose.connection.models[referencedCollectionName]) {
        throw new Error(
          `Referenced model "${referencedCollectionName}" not found`
        );
      }
      const ReferencedModel = mongoose.model(referencedCollectionName);
      let dataFromReferencedCollection;
      if (referencedCollectionName === "academyTopCourseCms") {
        const select = "_id coachName courseName description images fees";
        dataFromReferencedCollection = await ReferencedModel.find({}).populate({
          path: "course",
          select,
        });
      } else if (referencedCollectionName === "academyTopCoachCms") {
        const select =
          "_id firstName lastName profileImg status authStatus sportsCategories experience language";
        dataFromReferencedCollection = await ReferencedModel.find({}).populate({
          path: "coach",
          select,
        });
      } else {
        dataFromReferencedCollection = await ReferencedModel.find();
      }
      return {
        blockData: block,
        referencedData: dataFromReferencedCollection,
      };
    })
  );
  return new AppSuccess(res, {
    message: "Academy blocks fetched successfully",
    data: blocksWithReferencedData,
    statusCode: 200,
  });
});

export const getAcademyBlocksDataAdmin = catchAsync(async (req, res, next) => {
  if (!req.auth) {
    throw new AppError("Access not allowed", 400, {
      errors: [{ field: "auth", message: "Access not allowed" }],
    });
  }
  const academyId = req.user?.academyId?._id;
  if (!academyId) {
    throw new AppError("Academy ID not found in token", 400, {
      errors: [{ field: "academyId", message: "Academy ID is required" }],
    });
  }
  const academyBlockCms = await AcademyBlockCms.find({ academy: academyId });
  return new AppSuccess(res, {
    message: "Academy blocks fetched successfully",
    data: academyBlockCms,
    statusCode: 200,
  });
});

export const createAcademyBlock = catchAsync(async (req, res, next) => {
  if (!req.auth) {
    throw new AppError("Access not allowed", 400, {
      errors: [{ field: "auth", message: "Access not allowed" }],
    });
  }
  const academyId = req.user?.academyId?._id;
  if (!academyId) {
    throw new AppError("Academy ID not found in token", 400, {
      errors: [{ field: "academyId", message: "Academy ID is required" }],
    });
  }

  const academyBlockCms = await AcademyBlockCms.create({
    ...req.body,
    academy: academyId,
  });

  return new AppSuccess(res, {
    message: "Academy blocks created successfully",
    data: academyBlockCms,
    statusCode: 201,
  });
});

export const updateAcademyBlock = catchAsync(async (req, res, next) => {
  if (!req.auth) {
    throw new AppError("Access not allowed", 400, {
      errors: [{ field: "auth", message: "Access not allowed" }],
    });
  }
  const academyId = req.user?.academyId?._id;
  if (!academyId) {
    throw new AppError("Academy ID not found in token", 400, {
      errors: [{ field: "academyId", message: "Academy ID is required" }],
    });
  }
  const { id } = req.params;
  const academyBlockCms = await AcademyBlockCms.findByIdAndUpdate(
    id,
    {
      ...req.body,
      academy: academyId,
    },
    { new: true }
  );
  return new AppSuccess(res, {
    message: "Academy block updated successfully",
    data: academyBlockCms,
    statusCode: 200,
  });
});

export const updateAcademyBlockPosition = catchAsync(async (req, res, next) => {
  if (!req.auth) {
    throw new AppError("Access not allowed", 400, {
      errors: [{ field: "auth", message: "Access not allowed" }],
    });
  }
  const academyId = req.user?.academyId?._id;
  if (!academyId) {
    throw new AppError("Academy ID not found in token", 400, {
      errors: [{ field: "academyId", message: "Academy ID is required" }],
    });
  }
  const { updates } = req.body;
  const updatePromises = updates.map(({ id, newPosition }) => {
    return AcademyBlockCms.findByIdAndUpdate(
      id,
      { position: newPosition },
      { new: true }
    );
  });
  const results = await Promise.all(updatePromises);
  return new AppSuccess(res, {
    message: "Academy block position updated successfully",
    data: results,
    statusCode: 200,
  });
});

//Testimonials

export const getAcademyTestimonials = catchAsync(async (req, res, next) => {
  const academyId = req.params.academyId;
  const tokenAcademyId = req.user?.academyId?._id;
  if (!academyId) {
    throw new AppError("Academy ID not found in token", 400, {
      errors: [{ field: "academyId", message: "Academy ID is required" }],
    });
  }
  if (tokenAcademyId && academyId.toString() !== tokenAcademyId.toString()) {
    throw new AppError("Access not allowed", 403, {
      errors: [
        { field: "auth", message: "You are not authorized for this academy" },
      ],
    });
  }
  const testimonials = await AcademyTestimonial.find({
    academy: academyId,
  }).sort({ position: 1 });
  return new AppSuccess(res, {
    message: "Academy testimonials fetched successfully",
    data: testimonials,
    statusCode: 200,
  });
});

export const createAcademyTestimonials = catchAsync(async (req, res, next) => {
  if (!req.auth) {
    throw new AppError("Access not allowed", 400, {
      errors: [{ field: "auth", message: "Access not allowed" }],
    });
  }
  const academyId = req.user?.academyId?._id;
  if (!academyId) {
    throw new AppError("Academy ID not found in token", 400, {
      errors: [{ field: "academyId", message: "Academy ID is required" }],
    });
  }
  
  const count = await AcademyTestimonial.countDocuments({ academy: academyId });
  const block = await AcademyBlockCms.findOne({
    academy: academyId,
    collectionName: "academyTestimonial",
  });
  if (count > block.max) {
    throw new AppError(
      `Maximum limit reached, cannot be grater than ${block.max}`,
      400,
      {
        errors: [
          {
            field: "testimonials",
            message: `Maximum limit reached, cannot be grater than ${block.max}`,
          },
        ],
      }
    );
  }

  const academyTestimonial = await AcademyTestimonial.create({
    ...req.body,
    academy: academyId,
  });
  return new AppSuccess(res, {
    message: "Academy testimonial created successfully",
    data: academyTestimonial,
    statusCode: 201,
  });
});

export const updateAcademyTestimonials = catchAsync(async (req, res, next) => {
  if (!req.auth) {
    throw new AppError("Access not allowed", 400, {
      errors: [{ field: "auth", message: "Access not allowed" }],
    });
  }
  const academyId = req.user?.academyId?._id;
  if (!academyId) {
    throw new AppError("Academy ID not found in token", 400, {
      errors: [{ field: "academyId", message: "Academy ID is required" }],
    });
  }
  const { id } = req.params;
  const academyTestimonial = await AcademyTestimonial.findByIdAndUpdate(
    id,
    {
      ...req.body,
      academy: academyId,
    },
    { new: true }
  );
  return new AppSuccess(res, {
    message: "Academy testimonial updated successfully",
    data: academyTestimonial,
    statusCode: 200,
  });
});

export const updateAcademyTestimonialsPosition = catchAsync(
  async (req, res, next) => {
    if (!req.auth) {
      throw new AppError("Access not allowed", 400, {
        errors: [{ field: "auth", message: "Access not allowed" }],
      });
    }
    const academyId = req.user?.academyId?._id;
    if (!academyId) {
      throw new AppError("Academy ID not found in token", 400, {
        errors: [{ field: "academyId", message: "Academy ID is required" }],
      });
    }
    const { updates } = req.body;
    const updatePromises = updates.map(({ id, newPosition }) => {
      return AcademyTestimonial.findByIdAndUpdate(
        id,
        { $set: { position: Number(newPosition) }, academy: academyId },
        { new: true }
      );
    });
    const results = await Promise.all(updatePromises);
    return new AppSuccess(res, {
      message: "Academy testimonial position updated successfully",
      data: results,
      statusCode: 200,
    });
  }
);

export const deleteAcademyTestimonials = catchAsync(async (req, res, next) => {
  if (!req.auth) {
    throw new AppError("Access not allowed", 400, {
      errors: [{ field: "auth", message: "Access not allowed" }],
    });
  }
  const academyId = req.user?.academyId?._id;
  if (!academyId) {
    throw new AppError("Academy ID not found in token", 400, {
      errors: [{ field: "academyId", message: "Academy ID is required" }],
    });
  }
  const { id } = req.params;
  const academyTestimonial = await AcademyTestimonial.findByIdAndDelete(id);
  // Update positions of remaining testimonials
  const remainingTestimonials = await AcademyTestimonial.find({
    academy: academyId,
  }).sort({ position: 1 });
  for (let i = 0; i < remainingTestimonials.length; i++) {
    remainingTestimonials[i].position = i + 1;
    await remainingTestimonials[i].save();
  }
  return new AppSuccess(res, {
    message: "Academy testimonial deleted successfully",
    data: academyTestimonial,
    statusCode: 200,
  });
});
