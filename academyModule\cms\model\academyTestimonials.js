import mongoose from "mongoose";
const { Schema, model } = mongoose;
const academyTestimonialSchema = new Schema(
  {
    academy: { type: Schema.Types.ObjectId, ref: "academy" },
    name: {
      type: String,
    },
    description: {
      type: String,
    },
    position: {
      type: Number,
    },
  },
  { timestamps: true }
);

// Pre-save middleware to automatically assign position
academyTestimonialSchema.pre("save", async function (next) {
  try {
    if (!this.isNew || typeof this.position !== "undefined") {
      return next(); // If not new or position already defined, skip
    }

    const maxPosition = await this.constructor.findOne(
      { academy: this.academy }, // Scope to this academy
      {},
      { sort: { position: -1 } }
    ); // Find the document with maximum position
    this.position = maxPosition ? maxPosition.position + 1 : 1; // Assign the next position
    next();
  } catch (error) {
    next(error);
  }
});

export const AcademyTestimonial = model(
  "academyTestimonial",
  academyTestimonialSchema
);
