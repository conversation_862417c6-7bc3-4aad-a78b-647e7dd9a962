import mongoose from "mongoose";
const { Schema, model } = mongoose;

const calendarSchema = new Schema(
    {
        email: {
            required: true,
            type: String
        },
        refreshToken: {
            required: true,
            type: String
        }
    }
)

calendarSchema.index({ email: 1, refereshToken: 1 }, { unique: true });

export const Calendar = model("calendar",calendarSchema)